// Package cmd provides command line interface functionality for pulse.
package cmd

import (
	"git.homecredit.net/country/vn/platform/pulse/cmd/dnscmd"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	cfgFile string
	verbose bool
)

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() error {
	cmd := &cobra.Command{
		Use:   "pulse",
		Short: "DNS management and backup tool",
		Long: `A comprehensive DNS management tool that provides backup and restore
capabilities for DNS records with Git integration for version control.`,
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			// Configure viper with config file if provided
			if cfgFile != "" {
				viper.SetConfigFile(cfgFile)
			}

			// Reset cached config to force reload with new viper settings
			config.Reset()

			// Load configuration now that flags are parsed
			_, _ = config.Load() // Intentionally ignore error as config is optional

			// Set up logging based on verbose flag
			if verbose {
				logger.SetLevel(logger.LevelDebug)
			}
			return nil
		},
	}

	// Global flags
	cmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is /app/pulse.yaml)")
	cmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose output")

	// Add commands
	cmd.AddCommand(dnscmd.NewRootCommand(nil)) // DNS command will load its own configuration
	cmd.AddCommand(newVersionCmd())

	return cmd.Execute()
}
