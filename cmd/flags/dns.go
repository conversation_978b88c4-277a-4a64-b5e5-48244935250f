// Package flags provides common flag definitions and utilities for command line interfaces.
package flags

import (
	"fmt"
	"os"

	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"git.homecredit.net/country/vn/platform/pulse/pkg/vault"
	"github.com/spf13/cobra"
)

// Environment variable names for DNS operations
const (
	// Environment variable names
	EnvCluster        = "CLUSTER"
	EnvZones          = "ZONES"
	EnvDNSAPIToken    = "DNS_API_TOKEN"
	EnvGitLabAPIToken = "GITLAB_TOKEN"

	// DNS specific environment variables
	EnvDNSCluster = "DNS_CLUSTER"
	EnvDNSZones   = "DNS_ZONES"

	// Vault environment variables
	EnvVaultAddr  = "VAULT_TOOLS_ADDR"
	EnvVaultToken = "VAULT_TOOLS_TOKEN"
)

// Flag addition functions

// AddClusterFlag adds the cluster flag to a command
func AddClusterFlag(cmd *cobra.Command) {
	cmd.Flags().String("cluster", "", fmt.Sprintf("DNS cluster name (default from %s)", EnvDNSCluster))
}

// AddZonesFlag adds the zones flag to a command
func AddZonesFlag(cmd *cobra.Command) {
	cmd.Flags().String("zones", "", fmt.Sprintf("Comma-separated list of zones to process (default from %s)", EnvDNSZones))
}

// AddGitLabTokenFlag adds the GitLab API token flag to a command
func AddGitLabTokenFlag(cmd *cobra.Command) {
	cmd.Flags().String("gitlab-token", "", fmt.Sprintf("GitLab API token for authentication (default from %s)", EnvGitLabAPIToken))
}

// AddDateFlag adds the date flag to a command
func AddDateFlag(cmd *cobra.Command) {
	cmd.Flags().String("date", "", "Date of backup to restore (YYYY-MM-DD)")
}

// AddDryRunFlag adds the dry-run flag to a command
func AddDryRunFlag(cmd *cobra.Command) {
	cmd.Flags().Bool("dry-run", false, "Show what would be done without making changes")
}

// getValueFromFlagOrEnv is a helper function to get values from flag or environment
func getValueFromFlagOrEnv(cmd *cobra.Command, flagName, envVar string) (string, error) {
	value := cmd.Flag(flagName).Value.String()
	if value == "" {
		value = os.Getenv(envVar)
	}
	if value == "" {
		return "", fmt.Errorf("%s not specified. Use --%s flag or %s environment variable", flagName, flagName, envVar)
	}
	return value, nil
}

// Flag getter functions

// GetCluster gets the cluster value from flag or environment variable
func GetCluster(cmd *cobra.Command) (string, error) {
	return getValueFromFlagOrEnv(cmd, "cluster", EnvDNSCluster)
}

// GetZones gets the zones value from flag or environment variable
func GetZones(cmd *cobra.Command) (string, error) {
	return getValueFromFlagOrEnv(cmd, "zones", EnvDNSZones)
}

// GetGitLabToken gets the GitLab API token value from flag or environment variable
func GetGitLabToken(cmd *cobra.Command) (string, error) {
	return getValueFromFlagOrEnv(cmd, "gitlab-token", EnvGitLabAPIToken)
}

// GetDNSToken gets the DNS API token value from Vault
func GetDNSToken(cmd *cobra.Command) (string, error) {
	// Get cluster name for Vault lookup
	cluster, err := GetCluster(cmd)
	if err != nil {
		return "", fmt.Errorf("cluster required for DNS token retrieval: %w", err)
	}

	// Get DNS token from Vault
	token, err := getDNSTokenFromVault(cluster)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve DNS API token from Vault: %w", err)
	}

	logger.Debugf("Retrieved DNS token for cluster '%s' from Vault", cluster)
	return token, nil
}

// getDNSTokenFromVault retrieves DNS token from Vault
func getDNSTokenFromVault(cluster string) (string, error) {
	// Get Vault address from config (with fallback to environment)
	vaultAddr := config.GetVaultAddr()
	vaultToken := os.Getenv(EnvVaultToken)
	if vaultAddr == "" || vaultToken == "" {
		return "", fmt.Errorf("vault not configured (missing vault address from config/env or %s)", EnvVaultToken)
	}

	// Check if TLS verification should be skipped (default: true)
	tlsSkipVerify := true
	if skipTLSEnv := os.Getenv("VAULT_SKIP_TLS"); skipTLSEnv != "" {
		tlsSkipVerify = skipTLSEnv != "false"
	}

	// Create Vault client
	vaultClient, err := vault.NewClient(&vault.Config{
		Address:       vaultAddr,
		Token:         vaultToken,
		TLSSkipVerify: tlsSkipVerify,
	})
	if err != nil {
		return "", fmt.Errorf("failed to create Vault client: %w", err)
	}

	// Get cluster configuration to get the data key
	clusterCfg, err := config.GetCluster(cluster)
	if err != nil {
		return "", fmt.Errorf("failed to get cluster configuration: %w", err)
	}

	if clusterCfg.APITokenDataKey == "" {
		return "", fmt.Errorf("api_token_data_key not configured for cluster '%s'", cluster)
	}

	// Get the Vault path for DNS credentials
	vaultPath, err := config.GetDNSVaultPath()
	if err != nil {
		return "", fmt.Errorf("failed to get DNS vault path: %w", err)
	}

	// Retrieve the secret value from Vault
	token, err := vaultClient.GetSecretValue(vaultPath, clusterCfg.APITokenDataKey)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve DNS token from Vault path '%s' with data key '%s': %w", vaultPath, clusterCfg.APITokenDataKey, err)
	}

	return token, nil
}

// GetDate gets the date value from flag
func GetDate(cmd *cobra.Command) (string, error) {
	date := cmd.Flag("date").Value.String()
	if date == "" {
		return "", fmt.Errorf("restore date not specified. Use --date flag")
	}
	return date, nil
}

// GetDryRun gets the dry-run value from flag
func GetDryRun(cmd *cobra.Command) (bool, error) {
	return cmd.Flag("dry-run").Value.String() == "true", nil
}
