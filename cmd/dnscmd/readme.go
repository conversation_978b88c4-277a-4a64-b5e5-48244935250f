package dnscmd

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/internal/version"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
)

// Constants for date parsing and file naming
const (
	dateFormatLength        = 10 // Length of "YYYY-MM-DD" format
	minBackupFileNameLength = 25 // Minimum length for "dns-backup-x-2025-06-04"
	backupFilePrefix        = "dns-backup-"
	backupFileSuffix        = ".json"
)

// BackupData represents the data structure for README template
type BackupData struct {
	RepositoryName      string         `json:"repository_name"`
	ZoneName            string         `json:"zone_name"`
	BackupDate          string         `json:"backup_date"`
	BackupDateFormatted string         `json:"backup_date_formatted"`
	ClusterName         string         `json:"cluster_name"`
	RecordCount         int            `json:"record_count"`
	ARecordCount        int            `json:"a_record_count"`
	AAAARecordCount     int            `json:"aaaa_record_count"`
	CNAMERecordCount    int            `json:"cname_record_count"`
	MXRecordCount       int            `json:"mx_record_count"`
	TXTRecordCount      int            `json:"txt_record_count"`
	NSRecordCount       int            `json:"ns_record_count"`
	Changes             []ChangeInfo   `json:"changes"`
	MultipleZones       bool           `json:"multiple_zones"`
	Zones               []ZoneInfo     `json:"zones"`
	ToolVersion         string         `json:"tool_version"`
	BackupID            string         `json:"backup_id"`
	GitRepository       string         `json:"git_repository"`
	TeamName            string         `json:"team_name"`
	RecentChanges       []DailyChanges `json:"recent_changes"` // Changes from last 3 backup periods
}

// ChangeInfo represents a single DNS record change with date range context
type ChangeInfo struct {
	Action      string `json:"action"`      // "Added", "Modified", "Removed"
	Record      string `json:"record"`      // e.g., "www.example.com"
	Type        string `json:"type"`        // e.g., "A", "CNAME"
	Description string `json:"description"` // optional additional info
	FromDate    string `json:"from_date"`   // start date (YYYY-MM-DD)
	ToDate      string `json:"to_date"`     // end date (YYYY-MM-DD)
}

// DailyChanges represents changes between two consecutive backup dates
type DailyChanges struct {
	FromDate string       `json:"from_date"` // Previous backup date
	ToDate   string       `json:"to_date"`   // Next backup date
	Changes  []ChangeInfo `json:"changes"`   // Changes between these dates
}

// ZoneInfo represents backup information for a single zone
type ZoneInfo struct {
	Name             string       `json:"name"`
	RecordCount      int          `json:"record_count"`
	ARecordCount     int          `json:"a_record_count"`
	AAAARecordCount  int          `json:"aaaa_record_count"`
	CNAMERecordCount int          `json:"cname_record_count"`
	MXRecordCount    int          `json:"mx_record_count"`
	TXTRecordCount   int          `json:"txt_record_count"`
	NSRecordCount    int          `json:"ns_record_count"`
	Changes          []ChangeInfo `json:"changes"`
}

// DNSRecord represents a single DNS record for analysis
type DNSRecord struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Content  string `json:"content"`
	Value    string `json:"value"` // Alternative field name used in backup files
	TTL      int    `json:"ttl"`
	Priority *int   `json:"priority,omitempty"`
}

// BackupInfo holds information about a backup file
type BackupInfo struct {
	FilePath string
	Date     string
}

// getTeamName returns the team name from configuration with a sensible default
func getTeamName() string {
	cfg := config.New()
	// Try to get team name from config, fallback to environment, then default
	if teamName := cfg.GitBasePath; teamName != "" {
		// Extract team name from git base path (e.g., "country/vn/platform" -> "PlatformVN")
		parts := strings.Split(teamName, "/")
		if len(parts) > 0 {
			return "PlatformVN"
		}
	}
	return "PlatformVN" // Team name for Vietnam platform team
}

// generateREADME generates README.md from template and backup data
func generateREADME(templatePath string, data *BackupData, outputPath string) error {
	// Parse the template
	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		return fmt.Errorf("failed to parse template %s: %w", templatePath, err)
	}

	// Create output file
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file %s: %w", outputPath, err)
	}
	defer file.Close()

	// Execute template with data
	if err := tmpl.Execute(file, data); err != nil {
		return fmt.Errorf("failed to execute template: %w", err)
	}

	logger.Infof("Generated README.md: %s", outputPath)
	return nil
}

// analyzeBackupData analyzes backup files and creates BackupData
func analyzeBackupData(backupDir, cluster string, zones []string, gitRepoURL string, retentionDays int) (*BackupData, error) {
	data := &BackupData{
		RepositoryName:      fmt.Sprintf("dns-backup-%s", cluster),
		BackupDate:          utils.NowInVietnam().Format("2006-01-02 15:04:05"),
		BackupDateFormatted: utils.NowInVietnam().Format("2006-01-02"),
		ClusterName:         cluster,
		MultipleZones:       len(zones) > 1,
		ToolVersion:         version.GetVersionInfo(),
		BackupID:            fmt.Sprintf("%s-%d", cluster, utils.NowInVietnam().Unix()),
		GitRepository:       gitRepoURL,
		TeamName:            getTeamName(),
	}

	// Use retention days for change analysis, with a reasonable default
	changeDays := retentionDays
	if changeDays <= 0 {
		changeDays = 3 // Fallback to 3 days if retention is not configured
	}
	// Limit to a reasonable maximum for performance
	if changeDays > 30 {
		changeDays = 30
	}

	// If single zone, analyze it directly
	if len(zones) == 1 {
		zone := zones[0]
		zoneData, err := analyzeZoneBackup(backupDir, zone, changeDays)
		if err != nil {
			return nil, fmt.Errorf("failed to analyze zone %s: %w", zone, err)
		}

		data.ZoneName = zone
		data.RecordCount = zoneData.RecordCount
		data.ARecordCount = zoneData.ARecordCount
		data.AAAARecordCount = zoneData.AAAARecordCount
		data.CNAMERecordCount = zoneData.CNAMERecordCount
		data.MXRecordCount = zoneData.MXRecordCount
		data.TXTRecordCount = zoneData.TXTRecordCount
		data.NSRecordCount = zoneData.NSRecordCount
		data.Changes = zoneData.Changes

		// Get consecutive daily changes for this zone
		dailyChanges, err := detectConsecutiveDailyChanges(backupDir, zone, changeDays)
		if err != nil {
			logger.Warnf("Failed to detect daily changes for zone %s: %v", zone, err)
			data.RecentChanges = []DailyChanges{}
		} else {
			data.RecentChanges = dailyChanges
		}
	} else {
		// Multiple zones
		data.ZoneName = strings.Join(zones, ", ")
		totalRecords := 0

		for _, zone := range zones {
			zoneData, err := analyzeZoneBackup(backupDir, zone, changeDays)
			if err != nil {
				logger.Warnf("Failed to analyze zone %s: %v", zone, err)
				continue
			}

			data.Zones = append(data.Zones, *zoneData)
			totalRecords += zoneData.RecordCount
		}

		data.RecordCount = totalRecords

		// Get daily changes for all zones
		var allDailyChanges []DailyChanges
		for _, zone := range zones {
			dailyChanges, err := detectConsecutiveDailyChanges(backupDir, zone, changeDays)
			if err != nil {
				logger.Warnf("Failed to detect daily changes for zone %s: %v", zone, err)
				continue
			}
			allDailyChanges = append(allDailyChanges, dailyChanges...)
		}
		data.RecentChanges = mergeDailyChanges(allDailyChanges, changeDays)
	}

	return data, nil
}

// analyzeZoneBackup analyzes backup files for a single zone
func analyzeZoneBackup(backupDir, zone string, retentionDays int) (*ZoneInfo, error) {
	// Look for the most recent backup file for this zone
	backupFile, err := findLatestBackupFile(backupDir, zone)
	if err != nil {
		return nil, err
	}

	// Parse the backup file
	records, err := parseBackupFile(backupFile)
	if err != nil {
		return nil, err
	}

	// Analyze record types
	zoneInfo := &ZoneInfo{
		Name: zone,
	}

	for _, record := range records {
		zoneInfo.RecordCount++

		switch strings.ToUpper(record.Type) {
		case "A":
			zoneInfo.ARecordCount++
		case "AAAA":
			zoneInfo.AAAARecordCount++
		case "CNAME":
			zoneInfo.CNAMERecordCount++
		case "MX":
			zoneInfo.MXRecordCount++
		case "TXT":
			zoneInfo.TXTRecordCount++
		case "NS":
			zoneInfo.NSRecordCount++
		}
	}

	// Detect recent changes using retention days configuration
	changes, err := detectChanges(backupDir, zone, records, retentionDays)
	if err != nil {
		logger.Warnf("Failed to detect changes for zone %s: %v", zone, err)
		changes = []ChangeInfo{} // Empty changes if detection fails
	}
	zoneInfo.Changes = changes

	return zoneInfo, nil
}

// detectChanges compares recent consecutive backups for backward compatibility
func detectChanges(backupDir, zone string, currentRecords []DNSRecord, retentionDays int) ([]ChangeInfo, error) {
	// Get consecutive daily changes
	dailyChanges, err := detectConsecutiveDailyChanges(backupDir, zone, retentionDays)
	if err != nil {
		// No previous backup found, just return empty for current implementation
		return []ChangeInfo{}, nil
	}

	// Flatten daily changes into a single list for backward compatibility
	var allChanges []ChangeInfo
	for _, daily := range dailyChanges {
		allChanges = append(allChanges, daily.Changes...)
	}

	return allChanges, nil
}

// detectConsecutiveDailyChanges compares consecutive backup pairs to show daily changes
func detectConsecutiveDailyChanges(backupDir, zone string, maxDays int) ([]DailyChanges, error) {
	// Find recent backup files (sorted by date, newest first)
	recentBackups, err := findRecentBackupFiles(backupDir, zone, maxDays+1) // Need +1 to compare pairs
	if err != nil {
		return nil, err
	}

	if len(recentBackups) < 2 {
		return []DailyChanges{}, nil // Need at least 2 backups to compare
	}

	var dailyChanges []DailyChanges

	// Compare consecutive pairs (newest to oldest)
	for i := 0; i < len(recentBackups)-1 && len(dailyChanges) < maxDays; i++ {
		newerBackup := recentBackups[i]
		olderBackup := recentBackups[i+1]

		// Parse backup files
		newerRecords, err := parseBackupFile(newerBackup.FilePath)
		if err != nil {
			logger.Warnf("Failed to parse backup file %s: %v", newerBackup.FilePath, err)
			continue
		}

		olderRecords, err := parseBackupFile(olderBackup.FilePath)
		if err != nil {
			logger.Warnf("Failed to parse backup file %s: %v", olderBackup.FilePath, err)
			continue
		}

		// Compare the two consecutive backups
		changes := compareRecordsWithDateRange(newerRecords, olderRecords, olderBackup.Date, newerBackup.Date)

		if len(changes) > 0 {
			dailyChanges = append(dailyChanges, DailyChanges{
				FromDate: olderBackup.Date,
				ToDate:   newerBackup.Date,
				Changes:  changes,
			})
		}
	}

	return dailyChanges, nil
}

// findRecentBackupFiles finds the most recent backup files for a zone
func findRecentBackupFiles(backupDir, zone string, maxFiles int) ([]BackupInfo, error) {
	pattern := filepath.Join(backupDir, backupFilePrefix+"*.json")
	allMatches, err := filepath.Glob(pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to find backup files: %w", err)
	}

	var backupInfos []BackupInfo
	for _, match := range allMatches {
		filename := filepath.Base(match)
		if strings.HasPrefix(filename, backupFilePrefix) && strings.HasSuffix(filename, backupFileSuffix) {
			nameWithoutExt := strings.TrimSuffix(filename, backupFileSuffix)
			if strings.Contains(nameWithoutExt, "-") {
				parts := strings.Split(nameWithoutExt, "-")
				if len(parts) >= 4 {
					// Check if last 3 parts form a valid date (YYYY-MM-DD)
					lastThree := strings.Join(parts[len(parts)-3:], "-")
					if matched, _ := filepath.Match("????-??-??", lastThree); matched {
						// Valid date pattern found, extract zone name
						extractedZone := strings.Join(parts[2:len(parts)-3], "-")
						if extractedZone == zone {
							backupInfos = append(backupInfos, BackupInfo{
								FilePath: match,
								Date:     lastThree,
							})
						}
					}
				}
			}
		}
	}

	if len(backupInfos) == 0 {
		return nil, fmt.Errorf("no backup files found for zone %s", zone)
	}

	// Sort by date (newest first) and limit to maxFiles
	// Since we use YYYY-MM-DD format, string sorting works correctly
	for i := 0; i < len(backupInfos)-1; i++ {
		for j := i + 1; j < len(backupInfos); j++ {
			if backupInfos[i].Date < backupInfos[j].Date {
				backupInfos[i], backupInfos[j] = backupInfos[j], backupInfos[i]
			}
		}
	}

	if len(backupInfos) > maxFiles {
		backupInfos = backupInfos[:maxFiles]
	}

	return backupInfos, nil
}

// findLatestBackupFile finds the latest backup file for a zone
func findLatestBackupFile(backupDir, zone string) (string, error) {
	pattern := filepath.Join(backupDir, backupFilePrefix+"*.json")
	allMatches, err := filepath.Glob(pattern)
	if err != nil {
		return "", fmt.Errorf("failed to find backup files: %w", err)
	}

	var matches []string
	for _, match := range allMatches {
		filename := filepath.Base(match)
		if strings.HasPrefix(filename, backupFilePrefix) && strings.HasSuffix(filename, backupFileSuffix) {
			nameWithoutExt := strings.TrimSuffix(filename, backupFileSuffix)
			if strings.Contains(nameWithoutExt, "-") {
				parts := strings.Split(nameWithoutExt, "-")
				if len(parts) >= 4 {
					// Check if last 3 parts form a valid date (YYYY-MM-DD)
					lastThree := strings.Join(parts[len(parts)-3:], "-")
					if matched, _ := filepath.Match("????-??-??", lastThree); matched {
						// Valid date pattern found, extract zone name
						extractedZone := strings.Join(parts[2:len(parts)-3], "-")
						if extractedZone == zone {
							matches = append(matches, match)
						}
					}
				}
			}
		}
	}

	if len(matches) == 0 {
		return "", fmt.Errorf("no backup files found for zone %s in %s", zone, backupDir)
	}

	// Return the last one (should be the most recent due to naming convention)
	latest := matches[len(matches)-1]
	return latest, nil
}

// parseBackupFile parses a JSON backup file and returns DNS records
func parseBackupFile(filename string) ([]DNSRecord, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read backup file %s: %w", filename, err)
	}

	var records []DNSRecord

	// Try parsing as array first (current format)
	if err := json.Unmarshal(data, &records); err != nil {
		// Try parsing as object with "records" field (alternative format)
		var wrapper struct {
			Records []DNSRecord `json:"records"`
		}
		if err2 := json.Unmarshal(data, &wrapper); err2 != nil {
			return nil, fmt.Errorf("failed to parse backup file %s (tried both array and object format): %w", filename, err)
		}
		records = wrapper.Records
	}

	// Normalize records: if Content is empty but Value is set, use Value
	for i := range records {
		if records[i].Content == "" && records[i].Value != "" {
			records[i].Content = records[i].Value
		}
	}

	return records, nil
}

// compareRecordsWithDateRange compares records between two dates
func compareRecordsWithDateRange(newer, older []DNSRecord, fromDate, toDate string) []ChangeInfo {
	var changes []ChangeInfo

	// Create maps for easier comparison
	newerMap := make(map[string]DNSRecord)
	olderMap := make(map[string]DNSRecord)

	// Build newer records map
	for _, record := range newer {
		key := recordKey(record)
		newerMap[key] = record
	}

	// Build older records map
	for _, record := range older {
		key := recordKey(record)
		olderMap[key] = record
	}

	// Find added and modified records
	for key, newerRecord := range newerMap {
		if olderRecord, exists := olderMap[key]; exists {
			// Record exists in both, check if modified
			if !recordsEqual(newerRecord, olderRecord) {
				changes = append(changes, ChangeInfo{
					Action:      "Modified",
					Record:      formatRecordName(newerRecord),
					Type:        newerRecord.Type,
					Description: getModificationDescription(newerRecord, olderRecord),
					FromDate:    fromDate,
					ToDate:      toDate,
				})
			}
		} else {
			// Record only exists in newer (added)
			changes = append(changes, ChangeInfo{
				Action:      "Added",
				Record:      formatRecordName(newerRecord),
				Type:        newerRecord.Type,
				Description: fmt.Sprintf("New record: %s", newerRecord.Content),
				FromDate:    fromDate,
				ToDate:      toDate,
			})
		}
	}

	// Find removed records
	for key, olderRecord := range olderMap {
		if _, exists := newerMap[key]; !exists {
			// Record only exists in older (removed)
			description := fmt.Sprintf("Previously pointed to: %s", olderRecord.Content)
			changes = append(changes, ChangeInfo{
				Action:      "Removed",
				Record:      formatRecordName(olderRecord),
				Type:        olderRecord.Type,
				Description: description,
				FromDate:    fromDate,
				ToDate:      toDate,
			})
		}
	}

	return changes
}

// compareRecords compares current and previous records to detect changes (backward compatibility)
func compareRecords(current, previous []DNSRecord) []ChangeInfo {
	currentDate := utils.NowInVietnam().Format("2006-01-02")
	return compareRecordsWithDateRange(current, previous, currentDate, currentDate)
}

// mergeDailyChanges merges and deduplicates daily changes from multiple zones
func mergeDailyChanges(allChanges []DailyChanges, maxDays int) []DailyChanges {
	// Group changes by date range
	changeMap := make(map[string]*DailyChanges)

	for _, daily := range allChanges {
		key := fmt.Sprintf("%s→%s", daily.FromDate, daily.ToDate)
		if existing, exists := changeMap[key]; exists {
			existing.Changes = append(existing.Changes, daily.Changes...)
		} else {
			changeMap[key] = &DailyChanges{
				FromDate: daily.FromDate,
				ToDate:   daily.ToDate,
				Changes:  daily.Changes,
			}
		}
	}

	// Convert back to slice and sort by date
	var result []DailyChanges
	for _, daily := range changeMap {
		result = append(result, *daily)
	}

	// Sort by ToDate (newest first)
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].ToDate < result[j].ToDate {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	if len(result) > maxDays {
		result = result[:maxDays]
	}

	return result
}

// recordKey creates a unique key for a DNS record for comparison
func recordKey(record DNSRecord) string {
	return fmt.Sprintf("%s|%s", record.Name, record.Type)
}

// recordsEqual checks if two DNS records are equal
func recordsEqual(a, b DNSRecord) bool {
	if a.Name != b.Name || a.Type != b.Type || a.Content != b.Content || a.TTL != b.TTL {
		return false
	}

	if a.Priority == nil && b.Priority == nil {
		return true
	}
	if a.Priority == nil || b.Priority == nil {
		return false
	}
	return *a.Priority == *b.Priority
}

// formatRecordName formats a record name for display
func formatRecordName(record DNSRecord) string {
	if record.Name == "" || record.Name == "@" {
		// Root domain
		return "(root)"
	}
	return record.Name
}

// getModificationDescription describes what changed in a modified record
func getModificationDescription(current, previous DNSRecord) string {
	var changes []string

	if current.Content != previous.Content {
		changes = append(changes, fmt.Sprintf("content: %s → %s", previous.Content, current.Content))
	}

	if current.TTL != previous.TTL {
		changes = append(changes, fmt.Sprintf("TTL: %d → %d", previous.TTL, current.TTL))
	}

	if (current.Priority == nil) != (previous.Priority == nil) ||
		(current.Priority != nil && previous.Priority != nil && *current.Priority != *previous.Priority) {
		prevPrio := "none"
		currPrio := "none"
		if previous.Priority != nil {
			prevPrio = fmt.Sprintf("%d", *previous.Priority)
		}
		if current.Priority != nil {
			currPrio = fmt.Sprintf("%d", *current.Priority)
		}
		changes = append(changes, fmt.Sprintf("priority: %s → %s", prevPrio, currPrio))
	}

	if len(changes) == 0 {
		return "unknown change"
	}

	return strings.Join(changes, ", ")
}

// findAllClustersInRepo finds all cluster directories in the Git repository
func findAllClustersInRepo(repoDir string) ([]string, error) {
	var clusters []string

	entries, err := os.ReadDir(repoDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read repository directory: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() && entry.Name() != ".git" {
			// Check if this directory contains DNS backup files
			clusterDir := filepath.Join(repoDir, entry.Name())
			if hasBackupFiles(clusterDir) {
				clusters = append(clusters, entry.Name())
			}
		}
	}

	return clusters, nil
}

// hasBackupFiles checks if a directory contains DNS backup files
func hasBackupFiles(dir string) bool {
	pattern := filepath.Join(dir, "dns-backup-*.json")
	matches, err := filepath.Glob(pattern)
	return err == nil && len(matches) > 0
}

// analyzeAllClustersData analyzes backup data for all clusters in the repository
func analyzeAllClustersData(repoDir string, clusters []string, gitRepoURL string) (*BackupData, error) {
	if len(clusters) == 0 {
		return nil, fmt.Errorf("no clusters found in repository")
	}

	// If only one cluster, use the existing single-cluster logic
	if len(clusters) == 1 {
		return analyzeSingleCluster(repoDir, clusters[0], gitRepoURL)
	}

	// Multiple clusters - create comprehensive backup data
	data := createMultiClusterBackupData(clusters, gitRepoURL)

	// Get retention days from the first cluster configuration (fallback approach)
	// In a multi-cluster scenario, we use the retention setting from the first cluster
	retentionDays := 3 // Default fallback
	if len(clusters) > 0 {
		if clusterCfg, err := config.GetCluster(clusters[0]); err == nil {
			retentionDays = clusterCfg.RetentionDays
		}
	}

	// Analyze each cluster and aggregate results
	if err := analyzeAndAggregateClusterData(repoDir, clusters, data, retentionDays); err != nil {
		return nil, err
	}

	return data, nil
}

// analyzeSingleCluster handles the single cluster analysis case
func analyzeSingleCluster(repoDir, cluster, gitRepoURL string) (*BackupData, error) {
	clusterDir := filepath.Join(repoDir, cluster)
	zones, err := findZonesInCluster(clusterDir)
	if err != nil {
		return nil, fmt.Errorf("failed to find zones for cluster %s: %w", cluster, err)
	}

	// Get retention days from cluster configuration
	retentionDays := 3 // Default fallback
	if clusterCfg, err := config.GetCluster(cluster); err == nil {
		retentionDays = clusterCfg.RetentionDays
	}

	return analyzeBackupData(clusterDir, cluster, zones, gitRepoURL, retentionDays)
}

// createMultiClusterBackupData creates the base backup data structure for multiple clusters
func createMultiClusterBackupData(clusters []string, gitRepoURL string) *BackupData {
	return &BackupData{
		RepositoryName:      "dns-backup-multi-cluster",
		BackupDate:          utils.NowInVietnam().Format("2006-01-02 15:04:05"),
		BackupDateFormatted: utils.NowInVietnam().Format("2006-01-02"),
		ClusterName:         strings.Join(clusters, ", "),
		MultipleZones:       true, // Always true for multi-cluster
		ToolVersion:         version.GetVersionInfo(),
		BackupID:            fmt.Sprintf("multi-%d", utils.NowInVietnam().Unix()),
		GitRepository:       gitRepoURL,
		TeamName:            getTeamName(),
	}
}

// analyzeAndAggregateClusterData analyzes each cluster and aggregates the results
func analyzeAndAggregateClusterData(repoDir string, clusters []string, data *BackupData, retentionDays int) error {
	var totalRecords int
	var allZoneNames []string

	// Use retention days for change analysis, with a reasonable default
	changeDays := retentionDays
	if changeDays <= 0 {
		changeDays = 3 // Fallback to 3 days if retention is not configured
	}
	// Limit to a reasonable maximum for performance
	if changeDays > 30 {
		changeDays = 30
	}

	for _, cluster := range clusters {
		clusterZoneInfo, zoneNames, err := analyzeCluster(repoDir, cluster, changeDays)
		if err != nil {
			logger.Warnf("Failed to analyze cluster %s: %v", cluster, err)
			continue
		}

		data.Zones = append(data.Zones, *clusterZoneInfo)
		totalRecords += clusterZoneInfo.RecordCount
		allZoneNames = append(allZoneNames, zoneNames...)
	}

	data.RecordCount = totalRecords
	data.ZoneName = strings.Join(allZoneNames, ", ")

	// Aggregate daily changes from all clusters
	var allDailyChanges []DailyChanges
	for _, cluster := range clusters {
		clusterDir := filepath.Join(repoDir, cluster)
		zones, err := findZonesInCluster(clusterDir)
		if err != nil {
			continue
		}
		for _, zone := range zones {
			dailyChanges, err := detectConsecutiveDailyChanges(clusterDir, zone, changeDays)
			if err != nil {
				continue
			}
			allDailyChanges = append(allDailyChanges, dailyChanges...)
		}
	}
	data.RecentChanges = mergeDailyChanges(allDailyChanges, changeDays)

	return nil
}

// analyzeCluster analyzes a single cluster and returns its zone info and zone names
func analyzeCluster(repoDir, cluster string, retentionDays int) (*ZoneInfo, []string, error) {
	clusterDir := filepath.Join(repoDir, cluster)
	zones, err := findZonesInCluster(clusterDir)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to find zones for cluster %s: %w", cluster, err)
	}

	clusterZoneInfo := &ZoneInfo{
		Name: fmt.Sprintf("%s cluster", cluster),
	}

	var zoneNames []string

	// Analyze all zones in this cluster
	for _, zone := range zones {
		zoneData, err := analyzeZoneBackup(clusterDir, zone, retentionDays)
		if err != nil {
			logger.Warnf("Failed to analyze zone %s in cluster %s: %v", zone, cluster, err)
			continue
		}

		// Aggregate zone data into cluster info
		aggregateZoneData(clusterZoneInfo, zoneData)
		zoneNames = append(zoneNames, fmt.Sprintf("%s (%s)", zone, cluster))
	}

	return clusterZoneInfo, zoneNames, nil
}

// aggregateZoneData adds zone data to cluster zone info
func aggregateZoneData(clusterZoneInfo *ZoneInfo, zoneData *ZoneInfo) {
	clusterZoneInfo.RecordCount += zoneData.RecordCount
	clusterZoneInfo.ARecordCount += zoneData.ARecordCount
	clusterZoneInfo.AAAARecordCount += zoneData.AAAARecordCount
	clusterZoneInfo.CNAMERecordCount += zoneData.CNAMERecordCount
	clusterZoneInfo.MXRecordCount += zoneData.MXRecordCount
	clusterZoneInfo.TXTRecordCount += zoneData.TXTRecordCount
	clusterZoneInfo.NSRecordCount += zoneData.NSRecordCount
	clusterZoneInfo.Changes = append(clusterZoneInfo.Changes, zoneData.Changes...)
}

// findZonesInCluster finds all zones that have backup files in a cluster directory
func findZonesInCluster(clusterDir string) ([]string, error) {
	pattern := filepath.Join(clusterDir, "dns-backup-*.json")
	matches, err := filepath.Glob(pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to find backup files: %w", err)
	}

	zoneSet := make(map[string]bool)
	for _, match := range matches {
		filename := filepath.Base(match)
		// Extract zone from filename: dns-backup-{zone}-{YYYY-MM-DD}.json
		if strings.HasPrefix(filename, "dns-backup-") && strings.HasSuffix(filename, ".json") {
			nameWithoutExt := strings.TrimSuffix(filename, ".json")

			// Find the last occurrence of a date pattern (YYYY-MM-DD)
			// and extract everything before it as the zone name
			if strings.Contains(nameWithoutExt, "-") {
				// Split and check if last part matches date pattern
				parts := strings.Split(nameWithoutExt, "-")
				if len(parts) >= 4 {
					// Check if last 3 parts form a valid date (YYYY-MM-DD)
					lastThree := strings.Join(parts[len(parts)-3:], "-")
					if matched, _ := filepath.Match("????-??-??", lastThree); matched {
						// Valid date pattern found, zone is everything before the date
						zone := strings.Join(parts[2:len(parts)-3], "-")
						if zone != "" {
							zoneSet[zone] = true
						}
					}
				}
			}
		}
	}

	var zones []string
	for zone := range zoneSet {
		zones = append(zones, zone)
	}

	return zones, nil
}
