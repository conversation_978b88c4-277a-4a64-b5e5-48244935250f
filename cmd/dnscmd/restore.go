package dnscmd

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"git.homecredit.net/country/vn/platform/pulse/cmd/flags"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/dns"
	"git.homecredit.net/country/vn/platform/pulse/pkg/git"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"github.com/spf13/cobra"
)

// restore handles the DNS restore operation using cluster configuration
func restore(cmd *cobra.Command, args []string, manager *dns.Manager) error {
	// Setup common DNS operation context
	ctx, err := setupDNSOperation(cmd, manager)
	if err != nil {
		return err
	}

	// Get restore date from flag
	date, err := flags.GetDate(cmd)
	if err != nil {
		return fmt.Errorf("date error: %w", err)
	}

	// Get dry-run flag
	dryRun, err := flags.GetDryRun(cmd)
	if err != nil {
		return fmt.Errorf("dry-run flag error: %w", err)
	}

	logger.Infof("Starting restore operation for cluster %s...", ctx.Cluster)
	logger.Infof("Restore parameters: cluster=%s, zones=%v, date=%s, dry-run=%t",
		ctx.Cluster, ctx.Zones, date, dryRun)

	// Check if Git sync is needed for this cluster
	if ctx.ClusterCfg.GitProject != "" {
		if err := performGitSync(cmd, ctx.Cluster, ctx.ClusterCfg.BackupDir); err != nil {
			return fmt.Errorf("failed to sync backups from Git: %w", err)
		}
	}

	// Restore each zone using the cluster-based restore function
	for _, zone := range ctx.Zones {
		logger.Infof("Restoring zone: %s", zone)

		if err := ctx.Manager.RestoreFromCluster(ctx.Cluster, zone, date, dryRun); err != nil {
			return fmt.Errorf("failed to restore zone %s: %w", zone, err)
		}

		logger.Infof("Successfully processed restore for zone: %s", zone)
	}

	// Summary
	if dryRun {
		logger.Infof("Dry-run completed. No actual changes were made.")
		logger.Infof("Run without --dry-run flag to apply the changes.")
	} else {
		logger.Infof("Restore operation completed successfully for %d zone(s)", len(ctx.Zones))
	}

	return nil
}

// performGitSync handles syncing backup files from Git repository to local directory
func performGitSync(cmd *cobra.Command, cluster, backupDir string) error {
	logger.Infof("Syncing backup files from Git for cluster %s...", cluster)

	// Get GitLab token from flag or environment (for git operations)
	gitlabToken, err := flags.GetGitLabToken(cmd)
	if err != nil {
		return fmt.Errorf("GitLab token error: %w", err)
	}

	// Get global configuration
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Get cluster config to get the project name
	clusterCfg, err := config.GetCluster(cluster)
	if err != nil {
		return fmt.Errorf("failed to get cluster config: %w", err)
	}

	// Create global git config for the git client
	globalGitCfg := &config.GlobalGitConfig{
		BaseURL:  cfg.GitBaseURL,
		BasePath: cfg.GitBasePath,
	}

	// Create Git client with global git config and cluster-specific project name
	gitClient, err := git.NewClient(globalGitCfg, clusterCfg.GitProject, gitlabToken, clusterCfg.GitTmpDir)
	if err != nil {
		return fmt.Errorf("failed to create Git client: %w", err)
	}

	// Step 1: Clone/Initialize the Git repository to get current state
	if err := gitClient.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize Git repository: %w", err)
	}

	// Step 2: Copy backup files from Git repository to local backup directory
	if err := syncBackupsFromGit(gitClient, cluster, backupDir); err != nil {
		return fmt.Errorf("failed to copy backup files from Git: %w", err)
	}

	logger.Infof("Successfully synced backup files from Git repository for cluster %s", cluster)
	return nil
}

// syncBackupsFromGit copies backup files from Git repository to local directory
func syncBackupsFromGit(gitClient *git.Client, cluster, localBackupDir string) error {
	// Get the Git repository working directory
	gitWorkingDir := gitClient.GetWorkingDirectory()
	if gitWorkingDir == "" {
		return fmt.Errorf("git working directory not available")
	}

	// The Git repository contains files organized by cluster (e.g., int/, ext/)
	// Copy files from cluster subdirectory in Git repo to local backup directory
	gitClusterDir := filepath.Join(gitWorkingDir, cluster)

	// Check if cluster directory exists in Git repo
	if _, err := os.Stat(gitClusterDir); err != nil {
		if os.IsNotExist(err) {
			logger.Infof("No backup files found for cluster %s in Git repository", cluster)
			return nil // This is not an error - just no backups exist yet
		}
		return fmt.Errorf("failed to access Git cluster directory %s: %w", gitClusterDir, err)
	}

	// Ensure local backup directory exists
	if err := os.MkdirAll(localBackupDir, 0755); err != nil {
		return fmt.Errorf("failed to create local backup directory %s: %w", localBackupDir, err)
	}

	// Copy backup files from Git to local directory
	return copyGitBackupsToLocal(gitClusterDir, localBackupDir)
}

// copyGitBackupsToLocal copies backup files from Git repository to local directory
func copyGitBackupsToLocal(gitDir, localDir string) error {
	return filepath.Walk(gitDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Only copy DNS backup files
		if !strings.HasPrefix(info.Name(), "dns-backup-") || !strings.HasSuffix(info.Name(), ".json") {
			return nil
		}

		// Calculate destination path
		relPath, err := filepath.Rel(gitDir, path)
		if err != nil {
			return fmt.Errorf("failed to calculate relative path: %w", err)
		}

		destPath := filepath.Join(localDir, relPath)

		// Copy file
		return copyFile(path, destPath)
	})
}

// copyFile copies a file from src to dst
func copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer srcFile.Close()

	// Create destination directory if needed
	destDir := filepath.Dir(dst)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return fmt.Errorf("failed to create destination directory: %w", err)
	}

	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	return nil
}

// newRestoreCmd creates a new restore command
func newRestoreCmd(manager *dns.Manager) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "restore",
		Short: "Restore DNS records from backup",
		Long:  `Restore DNS records from a backup file for specified zones.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return restore(cmd, args, manager)
		},
	}

	// Add flags
	flags.AddClusterFlag(cmd)
	flags.AddZonesFlag(cmd)
	flags.AddDateFlag(cmd)
	flags.AddDryRunFlag(cmd)
	flags.AddGitLabTokenFlag(cmd)

	return cmd
}
