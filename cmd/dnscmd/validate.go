package dnscmd

import (
	"fmt"
	"os"
	"strings"

	"git.homecredit.net/country/vn/platform/pulse/cmd/flags"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/dns"
	"git.homecredit.net/country/vn/platform/pulse/pkg/git"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"github.com/spf13/cobra"
)

// Constants for architecture applications
const (
	archAppsFile = "applications.yml"
	archRepo     = "architecture/repository"
	archGitTmp   = "/tmp/arch"
)

// validate handles the DNS record validation operation
func validate(cmd *cobra.Command, args []string) error {
	configDir := "./config/update"
	if len(args) > 0 {
		configDir = args[0]
	}

	// Use config package to get the configured path if available
	if len(args) == 0 {
		configDir = config.GetDNSUpdateConfigPath()
	}

	logger.Infof("Validating DNS configuration files in %s...", configDir)

	var appCodes []string

	// Clone architecture repository to get app codes
	if err := cloneArchitectureRepo(cmd); err != nil {
		logger.Warnf("Failed to clone architecture repository: %v", err)
		logger.Warnf("App code validation against repository will be skipped")
		// Continue without failing - validation will skip repository check
	} else {
		// Load app codes from the cloned repository
		archAppFile := fmt.Sprintf("%s/%s/%s", archGitTmp, archRepo, archAppsFile)
		if _, err := os.Stat(archAppFile); os.IsNotExist(err) {
			return fmt.Errorf("architecture applications file %s does not exist", archAppFile)
		}
		logger.Infof("Loading app codes from architecture repository at %s", archAppFile)
		var err error
		if appCodes, err = dns.ExtractAppCodes(archAppFile); err != nil {
			logger.Warnf("Failed to extract app codes from %s: %v", archAppFile, err)
		} else {
			logger.Infof("Successfully loaded %d app codes from architecture repository", len(appCodes))
		}
	}

	// Use the validation function from DNS package to avoid circular imports
	updates, deletes, conflict, validationErrors, err := dns.LoadAndValidateRecordUpdates(configDir, appCodes)
	// Handle errors from loading and validating records
	if err != nil {
		return fmt.Errorf("failed to load and validate records: %w", err)
	}

	// Handle conflicts if any are found
	if conflict != nil {
		logger.Errorf("🚨 Conflict detected!")
		logger.Errorf("  Name:  %s", conflict.Name)
		logger.Errorf("  Type:  %s", conflict.Type)
		logger.Errorf("  In file 1: %s => %+v", conflict.File1, conflict.Record1)
		logger.Errorf("  In file 2: %s => %+v", conflict.File2, conflict.Record2)
		return fmt.Errorf("record conflict detected - please resolve conflicts before proceeding")
	}

	// Process validation errors
	var fileErrors = make(map[string][]string)
	for _, valErr := range validationErrors {
		key := valErr.File
		if key == "" {
			key = "general"
		}
		fileErrors[key] = append(fileErrors[key], fmt.Sprintf("%s: %s", valErr.Field, valErr.Message))
	}

	// Count statistics
	totalFiles := countUniqueFiles(updates, deletes)
	totalRecords := countTotalRecords(updates, deletes)
	validFiles := totalFiles - len(fileErrors)
	// Report file-by-file results
	if len(fileErrors) > 0 {
		logger.Errorf("Found validation errors in %d files:", len(fileErrors))
		for file, errors := range fileErrors {
			logger.Errorf("  ❌ File %s:", file)
			for _, errMsg := range errors {
				logger.Errorf("    - %s", errMsg)
			}
		}
	}

	// Display summary information about what would be processed
	displayValidationSummary(updates, deletes)

	// Report final summary
	logger.Infof("\n" + strings.Repeat("=", 50))
	logger.Infof("Validation Summary:")
	logger.Infof("  Total files examined: %d", totalFiles)
	logger.Infof("  Valid files: %d", validFiles)
	logger.Infof("  Invalid files: %d", len(fileErrors))
	logger.Infof("  Total DNS records: %d", totalRecords)
	logger.Infof("  Total validation errors: %d", len(validationErrors))

	if len(validationErrors) > 0 {
		return fmt.Errorf("validation found %d errors in %d files", 
			len(validationErrors), len(fileErrors))
	}

	logger.Infof("✓ All files validated successfully!")
	return nil
}

// cloneArchitectureRepo clones the architecture repository to get app codes
func cloneArchitectureRepo(cmd *cobra.Command) error {
	logger.Infof("Cloning architecture repository to validate app codes...")

	// Get GitLab token from flag or environment
	gitlabToken, err := flags.GetGitLabToken(cmd)
	if err != nil {
		return fmt.Errorf("GitLab token error: %w", err)
	}

	// Get global configuration
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Create global git config for the git client
	globalGitCfg := &config.GlobalGitConfig{
		BaseURL:  cfg.GitBaseURL,
		BasePath: "country/vn", // For architecture repository path
	}

	// Create Git client for architecture repository
	gitClient, err := git.NewClient(globalGitCfg, archRepo, gitlabToken, archGitTmp)
	if err != nil {
		return fmt.Errorf("failed to create Git client: %w", err)
	}

	// Clean up existing directory if it exists
	if err := os.RemoveAll(archGitTmp); err != nil {
		logger.Warnf("Failed to clean up existing directory %s: %v", archGitTmp, err)
	}

	// Clone/Initialize the Git repository
	if err := gitClient.Initialize(); err != nil {
		return fmt.Errorf("failed to clone architecture repository: %w", err)
	}

	logger.Infof("Successfully cloned architecture repository to %s", archGitTmp)
	return nil
}

// countUniqueFiles counts the number of unique files that would be processed
func countUniqueFiles(updates, deletes map[string]map[string][]dns.Record) int {
	// This is an approximation since we don't have direct file mapping
	// but it gives us a reasonable count based on clusters/zones
	fileCount := 0
	processedClusters := make(map[string]bool)
	
	for cluster := range updates {
		if !processedClusters[cluster] {
			fileCount++
			processedClusters[cluster] = true
		}
	}
	
	for cluster := range deletes {
		if !processedClusters[cluster] {
			fileCount++
			processedClusters[cluster] = true
		}
	}
	
	return fileCount
}

// countTotalRecords counts all DNS records across updates and deletes
func countTotalRecords(updates, deletes map[string]map[string][]dns.Record) int {
	total := 0
	
	for _, zones := range updates {
		for _, records := range zones {
			total += len(records)
		}
	}
	
	for _, zones := range deletes {
		for _, records := range zones {
			total += len(records)
		}
	}
	
	return total
}

// displayValidationSummary shows what records would be processed
func displayValidationSummary(updates, deletes map[string]map[string][]dns.Record) {
	// Show update records summary
	if len(updates) > 0 {
		logger.Infof("\n=== Records to Create or Update ===")
		for cluster, zones := range updates {
			logger.Infof("Cluster: %s", cluster)
			for zone, records := range zones {
				logger.Infof("  Zone: %s (%d records)", zone, len(records))
				// Show first few records as examples
				for i, record := range records {
					if i >= 3 { // Limit to first 3 records per zone
						logger.Infof("    ... and %d more", len(records)-3)
						break
					}
					logger.Infof("    - %s (%s) → %s [TTL: %d]", 
						record.Name, record.Type, record.Value, record.TTL)
				}
			}
		}
	}

	// Show delete records summary  
	if len(deletes) > 0 {
		logger.Infof("\n=== Records to Delete (state: absent) ===")
		for cluster, zones := range deletes {
			logger.Infof("Cluster: %s", cluster)
			for zone, records := range zones {
				logger.Infof("  Zone: %s (%d records)", zone, len(records))
				// Show first few records as examples
				for i, record := range records {
					if i >= 3 { // Limit to first 3 records per zone
						logger.Infof("    ... and %d more", len(records)-3)
						break
					}
					logger.Infof("    - %s (%s) → %s [TTL: %d]", 
						record.Name, record.Type, record.Value, record.TTL)
				}
			}
		}
	}
}

// newValidateCmd creates a new validate command
func newValidateCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate [directory]",
		Short: "Validate DNS configuration files",
		Long: `Validate DNS configuration files in the specified directory (defaults to ./config/update).

The validation performs the following checks:
1. Each file has an 'app_code' field that matches its filename (without .yaml extension)
2. All DNS records are properly formatted with valid names, types, values, and TTLs
3. Records marked for deletion (state: absent) have all required fields (name, type, value)
4. YAML syntax is valid and can be parsed
5. No duplicate records (same name and type) exist across files
6. App codes exist in the architecture repository (requires GitLab token)

Examples:
  pulse dns validate                    # Validate files in ./config/update
  pulse dns validate ./my-configs/dns  # Validate files in custom directory
  pulse dns validate --gitlab-token=xxx # Include architecture repository validation`,
		RunE: validate,
		Args: cobra.MaximumNArgs(1),
	}

	// Add GitLab token flag for architecture repository validation
	flags.AddGitLabTokenFlag(cmd)

	return cmd
}