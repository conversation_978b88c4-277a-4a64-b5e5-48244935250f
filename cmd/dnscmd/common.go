// Package dnscmd provides DNS management commands for backup and restore operations.
package dnscmd

import (
	"fmt"
	"strings"

	"git.homecredit.net/country/vn/platform/pulse/cmd/flags"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/dns"
	"github.com/spf13/cobra"
)

// dnsOperationContext holds common data needed for DNS operations
type dnsOperationContext struct {
	Cluster     string
	ClusterCfg  *config.DNSCluster
	DNSAPIToken string
	Zones       []string
	Manager     *dns.Manager
}

// setupDNSOperation prepares common DNS operation context from command flags
func setupDNSOperation(cmd *cobra.Command, providedManager *dns.Manager) (*dnsOperationContext, error) {
	// Get DNS API token from flag or environment variable
	dnsAPIToken, err := flags.GetDNSToken(cmd)
	if err != nil {
		return nil, fmt.Errorf("DNS API token error: %w", err)
	}

	// Validate DNS API token
	if strings.TrimSpace(dnsAPIToken) == "" {
		return nil, fmt.Errorf("DNS API token cannot be empty")
	}

	// Get cluster from flag or environment variable
	cluster, err := flags.GetCluster(cmd)
	if err != nil {
		return nil, fmt.Errorf("cluster error: %w", err)
	}

	// Validate cluster name
	if strings.TrimSpace(cluster) == "" {
		return nil, fmt.Errorf("cluster name cannot be empty")
	}

	// Get zones from flag or environment variable
	zonesStr, err := flags.GetZones(cmd)
	if err != nil {
		return nil, fmt.Errorf("zones error: %w", err)
	}

	// Validate zones string
	if strings.TrimSpace(zonesStr) == "" {
		return nil, fmt.Errorf("zones cannot be empty")
	}

	// Parse and validate zones
	zones, err := parseAndValidateZones(zonesStr)
	if err != nil {
		return nil, fmt.Errorf("zones validation error: %w", err)
	}

	// Get cluster-specific configuration
	clusterCfg, err := config.GetCluster(cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get configuration for cluster %s: %w", cluster, err)
	}

	// Create DNS manager if not provided
	manager := providedManager
	if manager == nil {
		manager, err = dns.NewManager(clusterCfg.BackupDir)
		if err != nil {
			return nil, fmt.Errorf("failed to create DNS manager: %w", err)
		}
	}

	// Create DNS server and set it in manager
	dnsServer, err := dns.Login(cmd.Context(), clusterCfg.URL, dnsAPIToken)
	if err != nil {
		return nil, fmt.Errorf("failed to login to DNS server: %w", err)
	}
	manager.SetDNSServer(dnsServer)

	return &dnsOperationContext{
		Cluster:     cluster,
		ClusterCfg:  clusterCfg,
		DNSAPIToken: dnsAPIToken,
		Zones:       zones,
		Manager:     manager,
	}, nil
}

// parseAndValidateZones parses comma-separated zones and validates each one
func parseAndValidateZones(zonesStr string) ([]string, error) {
	zones := make([]string, 0)
	for _, zone := range strings.Split(zonesStr, ",") {
		zone = strings.TrimSpace(zone)
		if zone == "" {
			continue
		}

		// Validate zone name format
		if len(zone) > 253 {
			return nil, fmt.Errorf("zone name too long (max 253 characters): %s", zone)
		}

		// Basic DNS zone name validation
		if strings.ContainsAny(zone, " \t\n\r") {
			return nil, fmt.Errorf("zone name contains invalid characters: %s", zone)
		}

		zones = append(zones, zone)
	}

	if len(zones) == 0 {
		return nil, fmt.Errorf("at least one valid zone must be specified")
	}

	return zones, nil
}
