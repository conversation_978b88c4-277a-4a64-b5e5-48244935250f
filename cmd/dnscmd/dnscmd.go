// Package dnscmd provides DNS management commands for backup and restore operations.
package dnscmd

import (
	"git.homecredit.net/country/vn/platform/pulse/pkg/dns"
	"github.com/spf13/cobra"
)

// NewRootCommand creates a new root command for DNS operations
func <PERSON><PERSON>oot<PERSON>om<PERSON>(manager *dns.Manager) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dns",
		Short: "DNS management commands",
		Long:  `Commands for managing DNS records, including backup and restore operations.`,
	}

	// Add subcommands
	cmd.AddCommand(newBackupCmd(manager))
	cmd.AddCommand(newRestoreCmd(manager))
	cmd.AddCommand(newUpdateCmd(manager))
	cmd.AddCommand(newValidateCmd())

	return cmd
}
