package dnscmd

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.homecredit.net/country/vn/platform/pulse/cmd/flags"
	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/dns"
	"git.homecredit.net/country/vn/platform/pulse/pkg/git"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"github.com/spf13/cobra"
)

// backup handles the DNS backup operation
func backup(cmd *cobra.Command, args []string, manager *dns.Manager) error {
	// Setup common DNS operation context
	ctx, err := setupDNSOperation(cmd, manager)
	if err != nil {
		return err
	}

	// Prepare backup directory once before backing up all zones
	if err := ctx.Manager.PrepareForBackup(); err != nil {
		return fmt.Errorf("failed to prepare backup directory: %w", err)
	}

	// Backup each zone
	for _, zone := range ctx.Zones {
		if err := ctx.Manager.Backup(zone); err != nil {
			return fmt.Errorf("failed to backup zone %s: %w", zone, err)
		}
	}

	// Check if Git backup is enabled in configuration
	if ctx.ClusterCfg.PushToGit {
		// When Git is enabled, let Git handle the retention policy
		if err := performGitBackup(cmd, ctx.Cluster, ctx.ClusterCfg.BackupDir, ctx.Zones); err != nil {
			return fmt.Errorf("failed to backup to Git: %w", err)
		}
	}

	return nil
}

// performGitBackup handles backing up to Git repository using cluster-specific configuration
func performGitBackup(cmd *cobra.Command, cluster, backupDir string, zones []string) error {
	logger.Infof("Starting Git backup for cluster %s...", cluster)

	// Get GitLab token from flag or environment (for git operations)
	gitlabToken, err := flags.GetGitLabToken(cmd)
	if err != nil {
		return fmt.Errorf("GitLab token error: %w", err)
	}

	// Get global configuration
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Get cluster config to get the project name
	clusterCfg, err := config.GetCluster(cluster)
	if err != nil {
		return fmt.Errorf("failed to get cluster config: %w", err)
	}

	// Create global git config for the git client
	globalGitCfg := &config.GlobalGitConfig{
		BaseURL:  cfg.GitBaseURL,
		BasePath: cfg.GitBasePath,
	}

	// Create Git client with global git config and cluster-specific project name
	gitClient, err := git.NewClient(globalGitCfg, clusterCfg.GitProject, gitlabToken, clusterCfg.GitTmpDir)
	if err != nil {
		return fmt.Errorf("failed to create Git client: %w", err)
	}

	// Step 1: Clone/Initialize the Git repository to get current state
	if err := gitClient.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize Git repository: %w", err)
	}

	// Step 2: Copy new backup files to the Git repository (without cleaning existing files)
	// The copyBackupFiles method will preserve the cluster subfolder structure
	if err := gitClient.CopyBackupFiles(backupDir); err != nil {
		return fmt.Errorf("failed to copy backup files: %w", err)
	}

	// Step 2.5: Generate README.md for the backup repository
	if err := generateBackupREADME(gitClient, cluster, backupDir, zones); err != nil {
		logger.Warnf("Failed to generate README.md: %v", err)
		// Don't fail the entire backup process if README generation fails
	}

	// Step 3: Remove old backup files from Git repository based on retention policy
	// Updated to work with cluster subfolders
	if clusterCfg.RetentionDays > 0 {
		if err := removeOldBackupsFromGit(gitClient, cluster, clusterCfg.RetentionDays); err != nil {
			return fmt.Errorf("failed to remove old backups from Git: %w", err)
		}
	}

	// Step 4: Stage all changes (new files and deletions)
	if err := gitClient.StageChanges(); err != nil {
		return fmt.Errorf("failed to stage changes: %w", err)
	}

	// Step 5: Check if there are any changes to commit
	hasChanges, err := gitClient.HasChanges()
	if err != nil {
		return fmt.Errorf("failed to check for changes: %w", err)
	}

	if !hasChanges {
		logger.Infof("No changes detected in Git repository for cluster %s, skipping commit and push", cluster)
		logger.Infof("DNS backup completed successfully - Git repository is up to date: %s", gitClient.GetRepositoryURL())
		return nil
	}

	// Step 6: Commit the changes
	timestamp := utils.NowInVietnam().Format("2006-01-02 15:04:05")
	commitMessage := fmt.Sprintf("DNS backup update for %s - %s", cluster, timestamp)

	if err := gitClient.Commit(commitMessage); err != nil {
		return fmt.Errorf("failed to commit changes: %w", err)
	}

	// Step 7: Push to remote repository
	if err := gitClient.Push(); err != nil {
		return fmt.Errorf("failed to push to remote: %w", err)
	}

	logger.Infof("Successfully backed up DNS records for cluster %s to Git repository: %s", cluster, gitClient.GetRepositoryURL())
	return nil
}

// removeOldBackupsFromGit removes old backup files from the Git repository based on retention policy
func removeOldBackupsFromGit(gitClient *git.Client, cluster string, retentionDays int) error {
	if retentionDays <= 0 {
		return nil // No retention policy
	}

	clusterDir, err := getClusterDirectory(gitClient, cluster)
	if err != nil {
		return err
	}

	// No cluster directory exists yet, nothing to clean
	if clusterDir == "" {
		return nil
	}

	backupFiles, err := findBackupFiles(clusterDir)
	if err != nil {
		return fmt.Errorf("failed to read cluster directory %s: %w", clusterDir, err)
	}

	cutoff := utils.NowInVietnam().AddDate(0, 0, -retentionDays)
	removedCount := removeExpiredBackups(gitClient, cluster, backupFiles, cutoff, retentionDays)

	if removedCount > 0 {
		logger.Infof("Removed %d old backup files from Git repository for cluster %s (retention: %d days)", removedCount, cluster, retentionDays)
	}

	return nil
}

// getClusterDirectory returns the cluster directory path if it exists
func getClusterDirectory(gitClient *git.Client, cluster string) (string, error) {
	repoDir := gitClient.GetWorkingDirectory()
	if repoDir == "" {
		return "", fmt.Errorf("git repository working directory not available")
	}

	clusterDir := filepath.Join(repoDir, cluster)
	if _, err := os.Stat(clusterDir); os.IsNotExist(err) {
		return "", nil // No cluster subfolder exists yet
	}

	return clusterDir, nil
}

// findBackupFiles returns all backup files in the cluster directory
func findBackupFiles(clusterDir string) ([]os.DirEntry, error) {
	entries, err := os.ReadDir(clusterDir)
	if err != nil {
		return nil, err
	}

	var backupFiles []os.DirEntry
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		filename := entry.Name()
		// Only process DNS backup files
		if strings.HasPrefix(filename, "dns-backup-") && strings.HasSuffix(filename, ".json") {
			backupFiles = append(backupFiles, entry)
		}
	}

	return backupFiles, nil
}

// removeExpiredBackups removes backup files older than the cutoff date
func removeExpiredBackups(gitClient *git.Client, cluster string, backupFiles []os.DirEntry, cutoff time.Time, retentionDays int) int {
	removedCount := 0
	const minBackupFileNameLength = 25 // Minimum length for "dns-backup-x-2025-06-04"
	const dateFormatLength = 10        // Length of "YYYY-MM-DD" format

	for _, entry := range backupFiles {
		filename := entry.Name()

		// Extract date from filename: dns-backup-{zone}-{YYYY-MM-DD}.json
		nameWithoutExt := strings.TrimSuffix(filename, ".json")
		if len(nameWithoutExt) < minBackupFileNameLength {
			continue
		}

		expectedDateStart := len(nameWithoutExt) - dateFormatLength
		if expectedDateStart <= 0 || nameWithoutExt[expectedDateStart-1] != '-' {
			continue
		}

		dateStr := nameWithoutExt[expectedDateStart:]
		backupDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			logger.Warnf("Invalid date format in backup filename %s: %v", filename, err)
			continue
		}

		if backupDate.Before(cutoff) {
			// Remove old backup file from Git (include cluster subfolder in path)
			clusterFilePath := filepath.Join(cluster, filename)
			if err := gitClient.RemoveFile(clusterFilePath); err != nil {
				logger.Warnf("Failed to remove old backup file %s from Git: %v", clusterFilePath, err)
				continue
			}
			logger.Infof("Removed old backup file from Git: %s (older than %d days)", clusterFilePath, retentionDays)
			removedCount++
		}
	}

	return removedCount
}

// generateBackupREADME generates README.md for the backup repository with all clusters
func generateBackupREADME(gitClient *git.Client, cluster, backupDir string, zones []string) error {
	// Get the Git repository working directory
	repoDir := gitClient.GetWorkingDirectory()
	if repoDir == "" {
		return fmt.Errorf("git repository working directory not available")
	}

	// Check if README template exists
	templatePath := "README.tmpl"
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		logger.Warnf("README template not found at %s, skipping README generation", templatePath)
		return nil
	}

	// Find all clusters in the repository
	allClusters, err := findAllClustersInRepo(repoDir)
	if err != nil {
		logger.Warnf("Failed to find all clusters in repository: %v", err)
		allClusters = []string{cluster} // Fallback to current cluster only
	}

	// Analyze backup data for all clusters
	backupData, err := analyzeAllClustersData(repoDir, allClusters, gitClient.GetRepositoryURL())
	if err != nil {
		return fmt.Errorf("failed to analyze backup data: %w", err)
	}

	// Generate README.md in the Git repository root
	readmePath := filepath.Join(repoDir, "README.md")
	if err := generateREADME(templatePath, backupData, readmePath); err != nil {
		return fmt.Errorf("failed to generate README: %w", err)
	}

	logger.Infof("Generated README.md for backup repository with %d clusters: %s", len(allClusters), readmePath)
	return nil
}

// newBackupCmd creates a new backup command
func newBackupCmd(manager *dns.Manager) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "backup",
		Short: "Backup DNS records",
		Long:  `Backup DNS records for specified zones with cluster-specific configuration.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return backup(cmd, args, manager)
		},
	}

	// Add flags
	flags.AddClusterFlag(cmd)
	flags.AddZonesFlag(cmd)
	flags.AddGitLabTokenFlag(cmd) // GitLab API token for git operations
	flags.AddDryRunFlag(cmd)

	return cmd
}
