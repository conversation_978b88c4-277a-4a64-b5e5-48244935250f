package dnscmd

import (
    "fmt"
	"strings"
    "git.homecredit.net/country/vn/platform/pulse/pkg/dns"
    "git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"git.homecredit.net/country/vn/platform/pulse/cmd/flags"
    "github.com/spf13/cobra"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
)

// update handles the DNS update operation
func update(cmd *cobra.Command, args []string, manager *dns.Manager) error {
	// Setup common DNS operation context
	ctx, err := setupDNSOperation(cmd, manager)
	if err != nil {
		return err
	}

	// Get dry-run flag
	dryRun, err := flags.GetDryRun(cmd)
	if err != nil {
		return fmt.Errorf("dry-run flag error: %w", err)
	}
    
    logger.Infof("Starting update operation for cluster %s...", ctx.Cluster)

    // Get the configured DNS update path
    configPath := config.GetDNSUpdateConfigPath()

    // Load and validate record updates from the config directory using DNS package function
    updates, deletes, conflict, err := dns.LoadRecordUpdates(configPath)
    if err != nil {
        return fmt.Errorf("failed to load and validate record updates: %w", err)
    }

    // Handle conflicts if any are found
    if conflict != nil {
        fmt.Println("🚨 Conflict detected!")
        fmt.Printf("  Name:  %s\n", conflict.Name)
        fmt.Printf("  Type:  %s\n", conflict.Type)
        fmt.Printf("  In file 1: %s => %+v\n", conflict.File1, conflict.Record1)
        fmt.Printf("  In file 2: %s => %+v\n", conflict.File2, conflict.Record2)
        return fmt.Errorf("record conflict detected - please resolve conflicts before proceeding")
    }
    
    // Handle validation errors
    // if len(validationErrors) > 0 {
    //     logger.Errorf("Found %d validation errors:", len(validationErrors))
    //     for _, valErr := range validationErrors {
    //         logger.Errorf("  - %s: %s (%s)", valErr.File, valErr.Field, valErr.Message)
    //     }
    //     return fmt.Errorf("validation failed with %d errors - please fix before proceeding", len(validationErrors))
    // }
    
    // Display summary information
    displayUpdateSummary(updates, deletes, ctx.Cluster, ctx.Zones)

    // Process updates and deletes
    zonesStr := ""
    if len(ctx.Zones) > 0 {
        zonesStr = strings.Join(ctx.Zones, ", ")
    } else {
        zonesStr = "all zones"
    }
    
    if dryRun {
        logger.Infof("DRY RUN: Would update records for cluster %s, zones: %s", ctx.Cluster, zonesStr)
    } else {
        logger.Infof("Updating records for cluster %s, zones: %s", ctx.Cluster, zonesStr)
    }

    // Process updates - UpdateRecords will handle zone validation internally
    if err := ctx.Manager.UpdateRecords(ctx.Cluster, updates, deletes, dryRun); err != nil {
        return fmt.Errorf("failed to update records: %w", err)
    }

    if dryRun {
        logger.Infof("DRY RUN completed successfully. No changes were made.")
    } else {
        logger.Infof("DNS update completed successfully.")
    }

    return nil
}

// displayUpdateSummary prints a summary of the records to be updated or deleted
func displayUpdateSummary(updates, deletes map[string]map[string][]dns.Record, cluster string, zones []string) {
    // Print update records
    fmt.Println("\n=== Records to Create or Update ===")
    for clusterName, clusterZones := range updates {
        // Skip if specific cluster is requested and this isn't it
        if cluster != "" && cluster != clusterName {
            continue
        }
        
        fmt.Printf("Cluster: %s\n", clusterName)
        for zoneName, records := range clusterZones {
            // Skip if specific zones are requested and this isn't in the list
            if len(zones) > 0 {
                zoneMatch := false
                for _, z := range zones {
                    if z == zoneName {
                        zoneMatch = true
                        break
                    }
                }
                if !zoneMatch {
                    continue
                }
            }
            
            fmt.Printf("  Zone: %s\n", zoneName)
            for _, r := range records {
                fmt.Printf("    - Name: %s, Type: %s, Value: %s, TTL: %d, Class: %s\n",
                    r.Name, r.Type, r.Value, r.TTL, r.Class)
            }
        }
    }

    // Print delete records
    fmt.Println("\n=== Records to Delete (state: absent) ===")
    for clusterName, clusterZones := range deletes {
        // Skip if specific cluster is requested and this isn't it
        if cluster != "" && cluster != clusterName {
            continue
        }
        
        fmt.Printf("Cluster: %s\n", clusterName)
        for zoneName, records := range clusterZones {
            // Skip if specific zones are requested and this isn't in the list
            if len(zones) > 0 {
                zoneMatch := false
                for _, z := range zones {
                    if z == zoneName {
                        zoneMatch = true
                        break
                    }
                }
                if !zoneMatch {
                    continue
                }
            }
            
            fmt.Printf("  Zone: %s\n", zoneName)
            for _, r := range records {
                fmt.Printf("    - Name: %s, Type: %s, Value: %s, TTL: %d, Class: %s\n",
                    r.Name, r.Type, r.Value, r.TTL, r.Class)
            }
        }
    }
    
    fmt.Println() // Add empty line for better readability
}                                                

// newUpdateCmd creates a new update command
func newUpdateCmd(manager *dns.Manager) *cobra.Command {
    cmd := &cobra.Command{
        Use:   "update",
        Short: "Update DNS records from a YAML file",
        Long:  `Update DNS records for specified zones based on the records defined in ./config/update YAML files.
        
Records marked with state: absent will be deleted, while others will be created or updated.
If multiple files define the same record (same name and type), a conflict will be reported.`,
        RunE:  func(cmd *cobra.Command, args []string) error {
			return update(cmd, args, manager)
        },
    }

    // Add flags
	flags.AddClusterFlag(cmd)
	flags.AddZonesFlag(cmd)
	flags.AddDryRunFlag(cmd)

    return cmd
}