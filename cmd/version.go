// Package cmd provides command line interface functionality for pulse.
package cmd

import (
	"fmt"

	"git.homecredit.net/country/vn/platform/pulse/internal/version"
	"github.com/spf13/cobra"
)

// newVersionCmd creates a new version command
func newVersionCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Print the version number of pulse",
		Long:  `Print the version number of pulse`,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println(version.GetVersionInfo())
		},
	}
}
