# Pulse

A simple DNS management and backup tool with Git integration and HashiCorp Vault support.

## Quick Start

### Build
```bash
git clone <repository>
cd pl-automation
go build -o pulse .
```

### Basic Usage
```bash
# Backup DNS records
pulse dns backup --cluster prod --zones example.com

# Restore DNS records  
pulse dns restore --cluster prod --zones example.com --date 2024-01-01

# Show version
pulse version
```

## Configuration

Create a `pulse.yaml` file in your working directory:

### Example Configuration

```yaml
global:
  git:
    base_url: "git.homecredit.vn"
    base_path: "country/vn/platform"
  creds:
    url: "https://vault.tools-country-k8s-pdc-green.hcnet.vn/"
    dns_path: "secret/data/platform/dns"

dns:
  prod:
    url: "https://dns-prod.example.com"
    backup_dir: "~/.pulse/backups/prod"
    git_project: "dns-backup-prod"
    git_tmp_dir: "~/.pulse/git-repos/prod"
    retention_days: 30
    push_to_git: true
    delete_extra_records: false  # Set to true for strict restore mode
    api_token_data_key: "dns_token"
  
  dev:
    url: "https://dns-dev.example.com"
    backup_dir: "~/.pulse/backups/dev"
    retention_days: 7
    delete_extra_records: true   # Enable strict restore for dev environment
```

### DNS Configuration Options

- **`url`**: DNS server API endpoint
- **`backup_dir`**: Local directory for backup files
- **`git_project`**: Git project name for backup storage (optional)
- **`git_tmp_dir`**: Temporary directory for Git operations (optional)
- **`retention_days`**: Number of days to keep backup files
- **`push_to_git`**: Whether to push backups to Git repository
- **`delete_extra_records`**: Whether to delete records that exist on server but not in backup during restore
  - `false` (default): Extra records are logged as warnings but left unchanged
  - `true`: Extra records are deleted to match backup exactly (strict restore mode)
- **`api_token_data_key`**: Vault data key for DNS API token

## Environment Variables

### Vault Configuration (Required for DNS operations)
```bash
# Vault server configuration
export VAULT_TOOLS_ADDR="https://vault.example.com"
export VAULT_TOOLS_TOKEN="your-vault-token"
```

### GitLab API Token (Required for Git operations)
```bash
# GitLab API token - for authenticating with GitLab for git operations
export GITLAB_TOKEN="your-gitlab-api-token"
```

### Optional Environment Variables
```bash
# Override command-line flags with environment variables
export DNS_CLUSTER="prod"
export DNS_ZONES="example.com,test.com"
```

## Commands

### DNS Backup
```bash
# Basic backup (requires Vault configuration)
pulse dns backup --cluster prod --zones example.com,test.com

# Using environment variables instead of flags
export DNS_CLUSTER="prod"
export DNS_ZONES="example.com,test.com"
pulse dns backup

# With specific GitLab token for Git operations
pulse dns backup --cluster prod --zones example.com --gitlab-token your-gitlab-token
```

### DNS Restore
```bash
# Basic restore (requires Vault configuration)
pulse dns restore --cluster prod --zones example.com --date 2024-01-01

# Dry run to see what would be restored
pulse dns restore --cluster prod --zones example.com --date 2024-01-01 --dry-run

# Using environment variables
export DNS_CLUSTER="prod"
export DNS_ZONES="example.com"
pulse dns restore --date 2024-01-01 --dry-run
```

## Token Resolution

The tool resolves API tokens as follows:

### DNS API Token
DNS API tokens are **always** retrieved from HashiCorp Vault using:
1. **Vault path**: Configured in `global.creds.dns`
2. **Data key**: Cluster-specific `api_token_data_key` from configuration
3. **Vault credentials**: `VAULT_TOOLS_ADDR` and `VAULT_TOOLS_TOKEN` environment variables

### GitLab API Token
1. **Command flag**: `--gitlab-token your-token`
2. **Environment variable**: `GITLAB_TOKEN`
3. **Error**: If none of the above are available

## Vault Secret Structure

Store DNS tokens in Vault at the configured path with cluster-specific keys:

```json
{
  "data": {
    "prod_token": "dns-api-token-for-production",
    "staging_token": "dns-api-token-for-staging"
  }
}
```

## Docker

```bash
# Build
docker build -t pulse .

# Run with config file and environment variables
docker run \
  -v $(pwd)/pulse.yaml:/app/pulse.yaml \
  -e VAULT_TOOLS_ADDR="https://vault.example.com" \
  -e VAULT_TOOLS_TOKEN="your-vault-token" \
  -e GITLAB_TOKEN="your-gitlab-token" \
  pulse dns backup --cluster prod --zones example.com
```

## Configuration Paths
- `./pulse.yaml` (highest priority)
- `./config.yaml` 
- `./configs/config.yaml`

Default directories:
- Backups: `~/.pulse/backups/{cluster}/`
- Git repos: `~/.pulse/git-repos/{cluster}/`