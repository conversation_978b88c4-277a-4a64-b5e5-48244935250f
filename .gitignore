# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
pulse

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

bin/
build/
vendor/
git-repos/

# Ignore all files in backups directory
backups/

.DS_Store
.claude/
