# Git Backup Integration

Pulse automatically backs up DNS records to GitLab repositories with version control.

## Quick Setup

1. **Configure API tokens**:
   ```bash
   # DNS API token - for authenticating with DNS management API  
   export DNS_API_TOKEN="your-dns-api-token"
   
   # GitLab API token - for git operations (push/pull)
   export GITLAB_TOKEN="your-gitlab-api-token"
   ```

2. **Create configuration**:
   ```yaml
   # pulse.yaml
   global:
     git:
       base_url: "git.homecredit.vn"
       base_path: "country/vn/k8s/deploy/backup"
   
   dns:
     int:
       url: "https://run-tools.homecredit.vn/dnsphpadmin/api.php"
       backup_dir: "backups/int"
       git_project: "dns-backup"  # Shared project
       retention_days: 30
       push_to_git: true
       api_token_data_key: "int_token"
     
     ext:
       url: "https://run-tools.homecredit.vn/dnsphpadmin_ext/api.php"
       backup_dir: "backups/ext"
       git_project: "dns-backup"  # Same shared project
       retention_days: 14
       push_to_git: true
       api_token_data_key: "ext_token"
   ```

3. **Run backup**:
   ```bash
   pulse dns backup --cluster int --zones example.com
   ```

## How It Works

1. **DNS Backup**: Records saved locally as JSON files (requires DNS API token)
2. **Git Repository**: Created automatically if doesn't exist (requires GitLab API token)
3. **Version Control**: Changes committed and pushed to GitLab (requires GitLab API token)
4. **Organization**: Multiple clusters can share the same Git project with automatic subfolder organization

## Repository Structure

```
Repository URL: https://git.homecredit.vn/country/vn/k8s/deploy/backup/dns-backup
Local Clone:    ~/.pulse/git-repos/dns-backup/

Files organized by cluster:
├── int/
│   ├── dns-backup-example.com-2024-01-01.json
│   ├── dns-backup-example.com-2024-01-02.json
│   └── dns-backup-test.com-2024-01-01.json
└── ext/
    ├── dns-backup-example.com-2024-01-01.json
    ├── dns-backup-example.com-2024-01-02.json
    └── dns-backup-public.com-2024-01-01.json
```

## Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `push_to_git` | Enable Git backup | `false` |
| `git_project` | GitLab project name (can be shared) | Required |
| `backup_dir` | Local backup directory | `~/.pulse/backups/{cluster}` |
| `retention_days` | Keep backups for N days | `30` |

**Note**: Multiple clusters can share the same `git_project`. Files are automatically organized into subfolders based on cluster names (e.g., `int/`, `ext/`).

## Examples

### Single Zone Backup
```bash
pulse dns backup --cluster int --zones example.com
```

### Multiple Zones
```bash
pulse dns backup --cluster ext --zones "example.com,test.com"
```

### With Custom Tokens
```bash
pulse dns backup --cluster int --zones example.com --dns-token your-dns-token --gitlab-token your-gitlab-token
```

### Backup Only (No Git Push)
If `push_to_git: false` in config, only DNS API token is needed:
```bash
pulse dns backup --cluster ext --zones example.com --dns-token your-dns-token
```

## Troubleshooting

**Repository not found**: Pulse creates repositories automatically if they don't exist.

**Permission denied**: Check your GitLab token has API access and project creation permissions.

**Config not found**: Ensure `pulse.yaml` is in your working directory or use `--config` flag.

**DNS authentication failed**: Verify your DNS API token is correct and has proper permissions.

## Security

### API Token Security
- **Never** put API tokens in config files
- Use environment variables: `DNS_API_TOKEN` and `GITLAB_TOKEN`
- Restrict GitLab token permissions to only necessary scopes
- Keep DNS API token secure and rotate regularly

### Token Requirements
- **DNS API Token**: Required for all DNS operations (backup/restore)
- **GitLab API Token**: Only required when `push_to_git: true` in cluster configuration 