// Package errors provides structured error handling for the pulse application.
package errors

import (
	"errors"
	"fmt"
)

// Error types for different categories of errors
var (
	// Configuration errors
	ErrConfigLoad    = fmt.Errorf("configuration load error")
	ErrConfigInvalid = fmt.Errorf("invalid configuration")

	// File system errors
	ErrFileNotFound  = fmt.Errorf("file not found")
	ErrFileOperation = fmt.Errorf("file operation error")
	ErrDirOperation  = fmt.Errorf("directory operation error")

	// DNS operation errors
	ErrDNSConnection = fmt.Errorf("dns server connection error")
	ErrDNSOperation  = fmt.Errorf("dns operation error")
	ErrZoneNotFound  = fmt.Errorf("dns zone not found")

	// Git operation errors
	ErrGitConnection = fmt.Errorf("git connection error")
	ErrGitOperation  = fmt.Errorf("git operation error")
	ErrRepoNotFound  = fmt.E<PERSON>rf("git repository not found")

	// Generic operation errors
	ErrInvalidInput = fmt.Errorf("invalid input")
	ErrTimeout      = fmt.Errorf("operation timeout")
)

// Wrap wraps an error with additional context
func Wrap(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// Wrapf wraps an error with formatted context
func Wrapf(err error, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", fmt.Sprintf(format, args...), err)
}

// New creates a new error with the given message
func New(message string) error {
	return errors.New(message)
}

// Newf creates a new error with formatted message
func Newf(format string, args ...interface{}) error {
	return fmt.Errorf(format, args...)
}
