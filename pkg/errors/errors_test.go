package errors

import (
	"fmt"
	"testing"
)

func TestWrap(t *testing.T) {
	originalErr := fmt.<PERSON><PERSON><PERSON>("original error")
	wrappedErr := Wrap(originalErr, "additional context")

	if wrappedErr == nil {
		t.<PERSON><PERSON>r("Wrap should not return nil for non-nil error")
	}

	expected := "additional context: original error"
	if wrappedErr.Error() != expected {
		t.<PERSON><PERSON>("Expected %q, got %q", expected, wrappedErr.Error())
	}
}

func TestWrapNil(t *testing.T) {
	wrappedErr := Wrap(nil, "context")
	if wrappedErr != nil {
		t.Error("Wrap should return nil for nil error")
	}
}

func TestWrapf(t *testing.T) {
	originalErr := fmt.Errorf("original error")
	wrappedErr := Wrapf(originalErr, "context %d", 42)

	expected := "context 42: original error"
	if wrappedErr.Error() != expected {
		t.<PERSON><PERSON>rf("Expected %q, got %q", expected, wrappedErr.Error())
	}
}

func TestNew(t *testing.T) {
	err := New("test error")
	if err == nil {
		t.Error("New should not return nil")
	}

	if err.Error() != "test error" {
		t.Errorf("Expected 'test error', got %q", err.Error())
	}
}

func TestNewf(t *testing.T) {
	err := Newf("test error %d", 42)
	if err == nil {
		t.Error("Newf should not return nil")
	}

	expected := "test error 42"
	if err.Error() != expected {
		t.Errorf("Expected %q, got %q", expected, err.Error())
	}
}
