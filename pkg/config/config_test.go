package config

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func resetConfig() {
	c = nil
	viper.Reset()
}

func TestNew(t *testing.T) {
	// Reset for clean test
	resetConfig()

	cfg := New()
	assert.NotNil(t, cfg)
	assert.Equal(t, "git.homecredit.vn", cfg.GitBaseURL)
	assert.Equal(t, "country/vn/platform", cfg.GitBasePath)
	assert.NotNil(t, cfg.DNSClusters)
}

func TestNewSingleton(t *testing.T) {
	// Reset for clean test
	resetConfig()

	cfg1 := New()
	cfg2 := New()
	assert.Same(t, cfg1, cfg2, "Should return the same instance")
}

func TestConfigWithFile(t *testing.T) {
	// Reset for clean test
	resetConfig()

	// Create test config
	tmpDir, err := os.MkdirTemp("", "config-test-*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	configContent := `global:
  git:
    base_url: "git.example.com"
    base_path: "test/path"
dns:
  prod:
    url: "https://dns-prod.example.com"
    backup_dir: "~/.pulse/backups/prod"
    git_project: "dns-backup-prod"
    git_tmp_dir: "~/.pulse/git-repos/prod"
    retention_days: 30
    push_to_git: true`

	configFile := filepath.Join(tmpDir, "pulse.yaml")
	err = os.WriteFile(configFile, []byte(configContent), 0644)
	require.NoError(t, err)

	// Change to temp directory so config is found
	originalDir, _ := os.Getwd()
	if err := os.Chdir(tmpDir); err != nil {
		t.Fatalf("Failed to change directory: %v", err)
	}
	defer func() {
		if err := os.Chdir(originalDir); err != nil {
			t.Errorf("Failed to restore directory: %v", err)
		}
	}()

	cfg := New()
	assert.Equal(t, "git.example.com", cfg.GitBaseURL)
	assert.Equal(t, "test/path", cfg.GitBasePath)
	assert.Len(t, cfg.DNSClusters, 1)

	prod := cfg.DNSClusters["prod"]
	require.NotNil(t, prod)
	assert.Equal(t, "https://dns-prod.example.com", prod.URL)
	assert.Equal(t, "dns-backup-prod", prod.GitProject)
	assert.Equal(t, 30, prod.RetentionDays)
	assert.True(t, prod.PushToGit)
}

func TestGetCluster(t *testing.T) {
	// Reset for clean test
	resetConfig()

	// Create test config
	tmpDir, err := os.MkdirTemp("", "cluster-test-*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	configContent := `dns:
  prod:
    url: "https://dns-prod.example.com"
    backup_dir: "/app/backups/prod"
    git_project: "dns-backup-prod"
    git_tmp_dir: "/tmp/git-repos/prod"
    retention_days: 30
    push_to_git: true`

	configFile := filepath.Join(tmpDir, "pulse.yaml")
	err = os.WriteFile(configFile, []byte(configContent), 0644)
	require.NoError(t, err)

	// Change to temp directory so config is found
	originalDir, _ := os.Getwd()
	if err := os.Chdir(tmpDir); err != nil {
		t.Fatalf("Failed to change directory: %v", err)
	}
	defer func() {
		if err := os.Chdir(originalDir); err != nil {
			t.Errorf("Failed to restore directory: %v", err)
		}
	}()

	cluster, err := GetCluster("prod")
	require.NoError(t, err)
	assert.Equal(t, "https://dns-prod.example.com", cluster.URL)
	assert.Equal(t, "dns-backup-prod", cluster.GitProject)

	// Test non-existent cluster
	_, err = GetCluster("nonexistent")
	assert.Error(t, err)
}

func TestGetDNSClusterURL(t *testing.T) {
	// Reset for clean test
	resetConfig()

	// Create test config
	tmpDir, err := os.MkdirTemp("", "url-test-*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	configContent := `dns:
  prod:
    url: "https://dns-prod.example.com"
    backup_dir: "/app/backups/prod"
    git_project: "dns-backup-prod"
    retention_days: 30
    push_to_git: true`

	configFile := filepath.Join(tmpDir, "pulse.yaml")
	err = os.WriteFile(configFile, []byte(configContent), 0644)
	require.NoError(t, err)

	// Change to temp directory so config is found
	originalDir, _ := os.Getwd()
	if err := os.Chdir(tmpDir); err != nil {
		t.Fatalf("Failed to change directory: %v", err)
	}
	defer func() {
		if err := os.Chdir(originalDir); err != nil {
			t.Errorf("Failed to restore directory: %v", err)
		}
	}()

	url, err := GetDNSClusterURL("prod")
	require.NoError(t, err)
	assert.Equal(t, "https://dns-prod.example.com", url)
}
