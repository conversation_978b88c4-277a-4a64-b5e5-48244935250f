// Package config provides configuration management for the pulse tool.
package config

import (
	"os"
	"path/filepath"

	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/errors"
	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	// Global Git settings
	GitBaseURL  string
	GitBasePath string

	// Global credentials configuration
	Creds *GlobalCreds

	// DNS clusters
	DNSClusters map[string]*DNSCluster
}

// GlobalCreds represents global credentials configuration
type GlobalCreds struct {
	URL     string // Vault server address
	DNSPath string // Path to DNS secret in Vault
}

// DNSCluster represents configuration for a specific DNS cluster
type DNSCluster struct {
	URL                string
	BackupDir          string
	GitProject         string
	GitTmpDir          string
	RetentionDays      int
	PushToGit          bool
	DeleteExtraRecords bool   // Delete records on server that don't exist in backup during restore
	APITokenDataKey    string // Data key from vault to extract DNS token
}

// GlobalGitConfig represents global Git settings (for git client interface)
type GlobalGitConfig struct {
	BaseURL  string
	BasePath string
}

var c *Config

// Reset clears the cached configuration to force reloading
func Reset() {
	c = nil
}

// defaultValues sets up default configuration values
func defaultValues() {
	defaults := map[string]any{
		"global.git.base_url":  "git.homecredit.vn",
		"global.git.base_path": "country/vn/platform",
		"global.creds.url":     "https://vault.tools-country-k8s-pdc-green.hcnet.vn/",
	}

	for key, value := range defaults {
		viper.SetDefault(key, value)
	}
}

// New returns the global configuration instance
func New() *Config {
	if c != nil {
		return c
	}

	defaultValues()
	c = &Config{
		DNSClusters: make(map[string]*DNSCluster),
	}

	// Set up config search paths and names
	viper.SetConfigType("yaml")

	// Check if a specific config file was set (via viper.SetConfigFile)
	if viper.ConfigFileUsed() != "" || viper.GetString("config") != "" {
		// A specific config file was set, try to read it directly
		viper.ReadInConfig() // Ignore error as config is optional
	} else {
		// No specific config file set, search in default locations
		viper.AddConfigPath(".")
		viper.AddConfigPath("./configs")

		configNames := []string{"pulse", "config"}
		for _, name := range configNames {
			viper.SetConfigName(name)
			if err := viper.ReadInConfig(); err == nil {
				break // Successfully loaded a config file
			}
		}
	}

	c.load()
	return c
}

// load populates the config struct from viper values
func (c *Config) load() {
	c.GitBaseURL = viper.GetString("global.git.base_url")
	c.GitBasePath = viper.GetString("global.git.base_path")

	// Load global credentials configuration
	if viper.IsSet("global.creds") {
		c.Creds = &GlobalCreds{
			URL:     viper.GetString("global.creds.url"),
			DNSPath: viper.GetString("global.creds.dns_path"),
		}
	}

	// Load DNS clusters
	dnsConfig := viper.GetStringMap("dns")
	for name := range dnsConfig {
		cluster := &DNSCluster{
			URL:                viper.GetString("dns." + name + ".url"),
			BackupDir:          utils.ExpandPath(viper.GetString("dns." + name + ".backup_dir")),
			GitProject:         viper.GetString("dns." + name + ".git_project"),
			GitTmpDir:          utils.ExpandPath(viper.GetString("dns." + name + ".git_tmp_dir")),
			RetentionDays:      viper.GetInt("dns." + name + ".retention_days"),
			PushToGit:          viper.GetBool("dns." + name + ".push_to_git"),
			DeleteExtraRecords: viper.GetBool("dns." + name + ".delete_extra_records"),
			APITokenDataKey:    viper.GetString("dns." + name + ".api_token_data_key"),
		}

		// Set default git tmp dir if not provided
		if cluster.GitTmpDir == "" {
			cluster.GitTmpDir = getDefaultTmpDirForCluster(name)
		}

		c.DNSClusters[name] = cluster
	}
}

// GetCluster returns the DNS cluster configuration
func GetCluster(cluster string) (*DNSCluster, error) {
	cfg := New()

	dnsCluster, exists := cfg.DNSClusters[cluster]
	if !exists {
		return nil, errors.Newf("cluster '%s' not found in configuration", cluster)
	}

	return dnsCluster, nil
}

// GetDNSClusterURL returns the DNS API URL for a cluster
func GetDNSClusterURL(cluster string) (string, error) {
	dnsCluster, err := GetCluster(cluster)
	if err != nil {
		return "", err
	}
	return dnsCluster.URL, nil
}

// GetAvailableClusters returns a list of all configured clusters
func GetAvailableClusters() ([]string, error) {
	cfg := New()

	var clusters []string
	for name := range cfg.DNSClusters {
		clusters = append(clusters, name)
	}

	return clusters, nil
}

// GetGlobalCreds returns the global credentials configuration
func GetGlobalCreds() (*GlobalCreds, error) {
	cfg := New()

	if cfg.Creds == nil {
		return nil, errors.New("global credentials not configured")
	}

	return cfg.Creds, nil
}

// GetDNSVaultPath returns the Vault path for DNS credentials
func GetDNSVaultPath() (string, error) {
	creds, err := GetGlobalCreds()
	if err != nil {
		return "", err
	}

	if creds.DNSPath == "" {
		return "", errors.New("DNS vault path not configured in global.creds.dns_path")
	}

	return creds.DNSPath, nil
}

// Load returns the global configuration (for backward compatibility)
func Load() (*Config, error) {
	return New(), nil
}

// Get returns the global configuration instance
func Get() *Config {
	return New()
}

// GetVaultAddr returns the Vault address from config or environment variable
func GetVaultAddr() string {
	cfg := New()

	// First try from credentials configuration
	if cfg.Creds != nil && cfg.Creds.URL != "" {
		return cfg.Creds.URL
	}

	// Fallback to environment variable
	return os.Getenv("VAULT_TOOLS_ADDR")
}

// getDefaultTmpDirForCluster returns the default temporary directory for git operations for a specific cluster
func getDefaultTmpDirForCluster(cluster string) string {
	if homeDir, err := os.UserHomeDir(); err == nil {
		return filepath.Join(homeDir, ".pulse", "git-repos", cluster)
	}
	// Container-friendly fallback (workdir /app)
	return filepath.Join("/tmp", "pulse", "git-repos", cluster)
}

// GetDNSUpdateConfigPath returns the path to DNS update configuration files
func GetDNSUpdateConfigPath() string {
	// Check if a custom path is configured
	if customPath := viper.GetString("dns.update_config_path"); customPath != "" {
		return utils.ExpandPath(customPath)
	}
	
	// Default path
	return "./config/update"
}

// GetDNSUpdateConfigPathForCluster returns the DNS update config path for a specific cluster
func GetDNSUpdateConfigPathForCluster(cluster string) (string, error) {
	// Check if cluster-specific path is configured
	if clusterPath := viper.GetString("dns." + cluster + ".update_config_path"); clusterPath != "" {
		return utils.ExpandPath(clusterPath), nil
	}
	
	// Fall back to global DNS update config path
	return GetDNSUpdateConfigPath(), nil
}

