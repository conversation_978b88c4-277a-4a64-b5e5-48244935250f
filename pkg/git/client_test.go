package git

import (
	"os"
	"path/filepath"
	"testing"

	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewClient(t *testing.T) {
	globalGitCfg := &config.GlobalGitConfig{
		BaseURL:  "git.example.com",
		BasePath: "test/path",
	}

	client, err := NewClient(globalGitCfg, "dns-backup-prod", "test-token", "")
	assert.NoError(t, err)
	assert.NotNil(t, client)
	assert.Equal(t, "git.example.com", client.baseURL)
	assert.Equal(t, "test/path", client.basePath)
	assert.Equal(t, "dns-backup-prod", client.project)
	assert.Equal(t, "test-token", client.gitlabAPIToken)
	assert.NotEmpty(t, client.repoPath)
}

func TestNewClientWithGitTmpDir(t *testing.T) {
	globalGitCfg := &config.GlobalGitConfig{
		BaseURL:  "git.example.com",
		BasePath: "test/path",
	}

	// Test with relative path
	client, err := NewClient(globalGitCfg, "dns-backup-prod", "test-token", "git-repos/test")
	assert.NoError(t, err)
	assert.NotNil(t, client)
	assert.Contains(t, client.repoPath, "git-repos/test/dns-backup-prod")

	// Test with home directory path
	client2, err := NewClient(globalGitCfg, "dns-backup-prod", "test-token", "~/git-repos/test")
	assert.NoError(t, err)
	assert.NotNil(t, client2)
	assert.NotContains(t, client2.repoPath, "~") // Should be expanded
	assert.Contains(t, client2.repoPath, "git-repos/test/dns-backup-prod")

	// Test with empty gitTmpDir (should use default)
	client3, err := NewClient(globalGitCfg, "dns-backup-prod", "test-token", "")
	assert.NoError(t, err)
	assert.NotNil(t, client3)
	assert.Contains(t, client3.repoPath, ".pulse/git-repos")
}

func TestExpandPath(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "home directory expansion",
			input:    "~/test/path",
			expected: "test/path", // Should contain this part
		},
		{
			name:     "absolute path",
			input:    "/absolute/path",
			expected: "/absolute/path",
		},
		{
			name:     "relative path",
			input:    "relative/path",
			expected: "relative/path",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ExpandPath(tt.input)
			if tt.input == "~/test/path" {
				// For home directory expansion, just check it doesn't start with ~
				assert.NotContains(t, result, "~")
				assert.Contains(t, result, tt.expected)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestGetRepositoryURL(t *testing.T) {
	tests := []struct {
		name     string
		client   *Client
		expected string
	}{
		{
			name: "with base path",
			client: &Client{
				baseURL:  "git.example.com",
				basePath: "group/subgroup",
				project:  "my-project",
			},
			expected: "https://git.example.com/group/subgroup/my-project.git",
		},
		{
			name: "without base path",
			client: &Client{
				baseURL: "gitlab.com",
				project: "my-project",
			},
			expected: "https://gitlab.com/my-project.git",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.client.getRepositoryURL()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCopyFile(t *testing.T) {
	// Create temporary directory
	tmpDir, err := os.MkdirTemp("", "git-copy-test-*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	srcFile := filepath.Join(tmpDir, "source.txt")
	dstFile := filepath.Join(tmpDir, "destination.txt")

	// Create source file
	content := "test content"
	require.NoError(t, os.WriteFile(srcFile, []byte(content), 0644))

	client := &Client{}

	// Test file copying
	err = client.copyFile(srcFile, dstFile)
	assert.NoError(t, err)

	// Verify content
	copiedContent, err := os.ReadFile(dstFile)
	require.NoError(t, err)
	assert.Equal(t, content, string(copiedContent))
}

func TestCopyBackupFiles(t *testing.T) {
	// Create temporary directories
	tmpDir, err := os.MkdirTemp("", "git-test-*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	backupDir := filepath.Join(tmpDir, "backups")
	repoDir := filepath.Join(tmpDir, "repo")

	// Create backup directory and files
	require.NoError(t, os.MkdirAll(backupDir, 0755))
	require.NoError(t, os.MkdirAll(repoDir, 0755))

	// Create test backup files
	testFiles := []string{
		"dns-backup-example.com-2024-01-01.json",
		"dns-backup-test.com-2024-01-01.json",
	}

	for _, filename := range testFiles {
		content := `[{"name":"test.example.com","type":"A","value":"1.1.1.1","ttl":300}]`
		require.NoError(t, os.WriteFile(filepath.Join(backupDir, filename), []byte(content), 0644))
	}

	client := &Client{
		repoPath: repoDir,
	}

	// Test copying backup files
	err = client.copyBackupFiles(backupDir)
	assert.NoError(t, err)

	// Check that files were copied (they should be in a subdirectory based on backup dir structure)
	for _, filename := range testFiles {
		// Files are copied with the backup directory structure preserved
		destFile := filepath.Join(repoDir, "backups", filename)
		assert.FileExists(t, destFile)

		// Verify content
		content, err := os.ReadFile(destFile)
		require.NoError(t, err)
		assert.Contains(t, string(content), "test.example.com")
	}
}
