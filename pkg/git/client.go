// Package git provides Git/GitLab integration functionality for pulse.
// It handles repository operations like cloning, committing, and pushing.
package git

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/errors"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
	"github.com/go-git/go-git/v5"
	gitconfig "github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing/object"
	githttp "github.com/go-git/go-git/v5/plumbing/transport/http"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

// Client wraps the git repository for backup operations
type Client struct {
	repo           *git.Repository
	gitlabClient   *gitlab.Client
	baseURL        string
	basePath       string
	project        string
	repoPath       string
	gitlabAPIToken string
}

// NewClient creates a new Git client with global GitLab configuration, project, and token
func NewClient(globalGitCfg *config.GlobalGitConfig, project, gitlabToken, gitTmpDir string) (*Client, error) {
	// Create GitLab SDK client
	gitlabClient, err := gitlab.NewClient(gitlabToken, gitlab.WithBaseURL(fmt.Sprintf("https://%s", globalGitCfg.BaseURL)))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create GitLab client")
	}

	// Use the provided git temp directory from cluster config
	var tmpDir string
	if gitTmpDir != "" {
		// If path starts with ~, expand it; otherwise use as-is (relative to current dir)
		if strings.HasPrefix(gitTmpDir, "~/") {
			tmpDir = utils.ExpandPath(gitTmpDir)
		} else {
			tmpDir = gitTmpDir
		}
	} else {
		// Fallback to default if not specified
		if homeDir, err := os.UserHomeDir(); err == nil {
			tmpDir = filepath.Join(homeDir, ".pulse", "git-repos")
		} else {
			tmpDir = "/tmp/pulse/git-repos"
		}
	}

	// Create client with repository path
	repoPath := filepath.Join(tmpDir, project)

	return &Client{
		gitlabClient:   gitlabClient,
		baseURL:        globalGitCfg.BaseURL,
		basePath:       globalGitCfg.BasePath,
		project:        project,
		repoPath:       repoPath,
		gitlabAPIToken: gitlabToken,
	}, nil
}

// Initialize sets up the Git repository for the configured project
func (c *Client) Initialize() error {
	if c.project == "" {
		return errors.New("git project not configured")
	}

	// Create directory structure
	if err := os.MkdirAll(c.repoPath, 0755); err != nil {
		return errors.Wrapf(err, "failed to create repository directory: %s", c.repoPath)
	}

	// Check if repository already exists
	if _, err := os.Stat(filepath.Join(c.repoPath, ".git")); err == nil {
		// Repository exists, try to open it
		repo, err := git.PlainOpen(c.repoPath)
		if err != nil {
			return errors.Wrapf(err, "failed to open existing repository at %s", c.repoPath)
		}
		c.repo = repo
		return nil
	}

	// Try to clone the repository first
	repoURL := c.getRepositoryURL()
	auth := &githttp.BasicAuth{
		Username: "oauth2",
		Password: c.gitlabAPIToken,
	}

	repo, err := git.PlainClone(c.repoPath, false, &git.CloneOptions{
		URL:          repoURL,
		Auth:         auth,
		SingleBranch: true,
	})

	if err != nil {
		if err == git.ErrRepositoryNotExists || strings.Contains(err.Error(), "repository not found") || strings.Contains(err.Error(), "404") {
			// Repository doesn't exist, create it
			if createErr := c.createRepository(); createErr != nil {
				return errors.Wrapf(createErr, "failed to create repository")
			}

			// Initialize local repository
			repo, err = git.PlainInit(c.repoPath, false)
			if err != nil {
				return errors.Wrapf(err, "failed to initialize local repository")
			}

			// Add remote origin
			_, err = repo.CreateRemote(&gitconfig.RemoteConfig{
				Name: "origin",
				URLs: []string{repoURL},
			})
			if err != nil {
				return errors.Wrapf(err, "failed to add remote origin")
			}
		} else {
			return errors.Wrapf(err, "failed to clone repository from %s", repoURL)
		}
	}

	c.repo = repo
	return nil
}

// AddFiles adds files from the backup directory to the repository (cleans repository first)
func (c *Client) AddFiles(backupDir string) error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	// Clean repository directory (keep .git) - removes all existing files first
	if err := c.cleanRepository(); err != nil {
		return errors.Wrapf(err, "failed to clean repository")
	}

	// Copy files from backup directory to repository
	if err := c.copyBackupFiles(backupDir); err != nil {
		return errors.Wrapf(err, "failed to copy backup files")
	}

	// Add all files to git
	worktree, err := c.repo.Worktree()
	if err != nil {
		return errors.Wrapf(err, "failed to get worktree")
	}

	_, err = worktree.Add(".")
	if err != nil {
		return errors.Wrapf(err, "failed to add files to git")
	}

	return nil
}

// Commit creates a commit with the backup files
func (c *Client) Commit(message string) error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	worktree, err := c.repo.Worktree()
	if err != nil {
		return errors.Wrapf(err, "failed to get worktree")
	}

	commit, err := worktree.Commit(message, &git.CommitOptions{
		Author: &object.Signature{
			Name:  "Pulse DNS Backup",
			Email: "<EMAIL>",
			When:  utils.NowInVietnam(),
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to create commit")
	}

	logger.Infof("Created commit: %s", commit.String())
	return nil
}

// Push pushes the changes to the remote repository
func (c *Client) Push() error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	auth := &githttp.BasicAuth{
		Username: "oauth2",
		Password: c.gitlabAPIToken,
	}

	err := c.repo.Push(&git.PushOptions{
		RemoteName: "origin",
		Auth:       auth,
	})
	if err != nil && err != git.NoErrAlreadyUpToDate {
		return errors.Wrapf(err, "failed to push to remote repository")
	}

	logger.Infof("Successfully pushed to %s", c.getRepositoryURL())
	return nil
}

// getRepositoryURL constructs the full repository URL
func (c *Client) getRepositoryURL() string {
	if c.basePath != "" {
		return fmt.Sprintf("https://%s/%s/%s.git", c.baseURL, c.basePath, c.project)
	}
	return fmt.Sprintf("https://%s/%s.git", c.baseURL, c.project)
}

// createRepository creates a new repository in GitLab
func (c *Client) createRepository() error {
	// Prepare project creation options
	createOptions := &gitlab.CreateProjectOptions{
		Name:        &c.project,
		Path:        &c.project,
		Description: gitlab.Ptr("DNS backup repository created by Pulse"),
		Visibility:  gitlab.Ptr(gitlab.PrivateVisibility),
	}

	// If basePath is specified, find or create the namespace
	if c.basePath != "" {
		namespaceID, err := c.getOrCreateNamespace()
		if err != nil {
			return errors.Wrapf(err, "failed to get/create namespace")
		}
		createOptions.NamespaceID = gitlab.Ptr(namespaceID)
	}

	// Create the project
	project, _, err := c.gitlabClient.Projects.CreateProject(createOptions)
	if err != nil {
		return errors.Wrapf(err, "failed to create GitLab project")
	}

	logger.Infof("Created GitLab repository: %s", project.WebURL)
	return nil
}

// getOrCreateNamespace gets the namespace ID for the basePath or creates it
func (c *Client) getOrCreateNamespace() (int, error) {
	// First, try to find the group by its full path directly
	fullPath := c.basePath
	groups, _, err := c.gitlabClient.Groups.ListGroups(&gitlab.ListGroupsOptions{
		Search: gitlab.Ptr(fullPath),
	})
	if err == nil {
		// Look for exact match by full path
		for _, group := range groups {
			if group.FullPath == fullPath {
				return group.ID, nil
			}
		}
	}

	// If direct search didn't work, fall back to step-by-step navigation
	parts := strings.Split(c.basePath, "/")
	parentID := 0

	for _, part := range parts {
		namespaceID, err := c.findNamespace(part, parentID)
		if err != nil {
			return 0, errors.Wrapf(err, "failed to find namespace '%s'", part)
		}
		parentID = namespaceID
	}

	return parentID, nil
}

// findNamespace finds a namespace by name and parent ID
func (c *Client) findNamespace(name string, parentID int) (int, error) {
	if parentID == 0 {
		// Search in root groups (not just namespaces)
		groups, _, err := c.gitlabClient.Groups.ListGroups(&gitlab.ListGroupsOptions{
			Search: gitlab.Ptr(name),
		})
		if err != nil {
			return 0, errors.Wrapf(err, "failed to search groups")
		}

		// Look for exact match
		for _, group := range groups {
			if group.Name == name || group.Path == name {
				return group.ID, nil
			}
		}

		// Also try namespaces for compatibility
		namespaces, _, err := c.gitlabClient.Namespaces.ListNamespaces(&gitlab.ListNamespacesOptions{
			Search: gitlab.Ptr(name),
		})
		if err == nil {
			// Look for exact match
			for _, ns := range namespaces {
				if ns.Name == name || ns.Path == name {
					return ns.ID, nil
				}
			}
		}
	} else {
		// Search in subgroups
		subgroups, _, err := c.gitlabClient.Groups.ListSubGroups(parentID, &gitlab.ListSubGroupsOptions{
			Search: gitlab.Ptr(name),
		})
		if err != nil {
			return 0, errors.Wrapf(err, "failed to search subgroups")
		}

		// Look for exact match
		for _, group := range subgroups {
			if group.Name == name || group.Path == name {
				return group.ID, nil
			}
		}
	}

	return 0, errors.Newf("namespace '%s' not found", name)
}

// cleanRepository removes all files except .git directory
func (c *Client) cleanRepository() error {
	entries, err := os.ReadDir(c.repoPath)
	if err != nil {
		return errors.Wrapf(err, "failed to read repository directory")
	}

	for _, entry := range entries {
		if entry.Name() == ".git" {
			continue
		}

		path := filepath.Join(c.repoPath, entry.Name())
		if err := os.RemoveAll(path); err != nil {
			return errors.Wrapf(err, "failed to remove %s", path)
		}
	}

	return nil
}

// copyBackupFiles copies files from backup directory to repository
func (c *Client) copyBackupFiles(backupDir string) error {
	return filepath.Walk(backupDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Calculate relative path from the parent of backupDir to preserve folder structure
		// This allows us to maintain subdirectories like "int/" and "ext/"
		parentDir := filepath.Dir(backupDir)
		relPath, err := filepath.Rel(parentDir, path)
		if err != nil {
			return errors.Wrapf(err, "failed to calculate relative path")
		}

		// Destination path in repository
		destPath := filepath.Join(c.repoPath, relPath)

		// Create destination directory if needed
		destDir := filepath.Dir(destPath)
		if err := os.MkdirAll(destDir, 0755); err != nil {
			return errors.Wrapf(err, "failed to create destination directory")
		}

		// Copy file
		return c.copyFile(path, destPath)
	})
}

// copyFile copies a file from src to dst
func (c *Client) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return errors.Wrapf(err, "failed to open source file: %s", src)
	}
	defer func() {
		if closeErr := srcFile.Close(); closeErr != nil {
			logger.Warnf("Failed to close source file: %v", closeErr)
		}
	}()

	dstFile, err := os.Create(dst)
	if err != nil {
		return errors.Wrapf(err, "failed to create destination file: %s", dst)
	}
	defer func() {
		if closeErr := dstFile.Close(); closeErr != nil {
			logger.Warnf("Failed to close destination file: %v", closeErr)
		}
	}()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return errors.Wrapf(err, "failed to copy file content")
	}

	return nil
}

// GetRepositoryURL returns the full repository URL
func (c *Client) GetRepositoryURL() string {
	return c.getRepositoryURL()
}

// GetWorkingDirectory returns the Git repository working directory path
func (c *Client) GetWorkingDirectory() string {
	return c.repoPath
}

// RemoveFile removes a file from the Git repository working directory and stages the removal
func (c *Client) RemoveFile(filename string) error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	// Full path to the file
	filePath := filepath.Join(c.repoPath, filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		// File doesn't exist, nothing to remove
		return nil
	}

	// Remove the file from filesystem
	if err := os.Remove(filePath); err != nil {
		return errors.Wrapf(err, "failed to remove file %s", filename)
	}

	// Stage the removal in Git
	worktree, err := c.repo.Worktree()
	if err != nil {
		return errors.Wrapf(err, "failed to get worktree")
	}

	_, err = worktree.Add(filename)
	if err != nil {
		return errors.Wrapf(err, "failed to stage file removal")
	}

	return nil
}

// CopyBackupFiles copies files from backup directory to repository without cleaning existing files
func (c *Client) CopyBackupFiles(backupDir string) error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	// Copy files from backup directory to repository (without cleaning existing files)
	return c.copyBackupFiles(backupDir)
}

// StageChanges stages all changes (new files, modifications, and deletions) in the repository
func (c *Client) StageChanges() error {
	if c.repo == nil {
		return errors.New("repository not initialized")
	}

	// Add all files to git
	worktree, err := c.repo.Worktree()
	if err != nil {
		return errors.Wrapf(err, "failed to get worktree")
	}

	_, err = worktree.Add(".")
	if err != nil {
		return errors.Wrapf(err, "failed to stage changes")
	}

	return nil
}

// HasChanges checks if there are any changes (staged or unstaged) in the repository
func (c *Client) HasChanges() (bool, error) {
	if c.repo == nil {
		return false, errors.New("repository not initialized")
	}

	worktree, err := c.repo.Worktree()
	if err != nil {
		return false, errors.Wrapf(err, "failed to get worktree")
	}

	status, err := worktree.Status()
	if err != nil {
		return false, errors.Wrapf(err, "failed to get repository status")
	}

	// Check if there are any changes (added, modified, deleted, etc.)
	return !status.IsClean(), nil
}
