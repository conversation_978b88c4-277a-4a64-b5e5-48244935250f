package vault

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewClient(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
			errorMsg:    "vault config is required",
		},
		{
			name: "valid config with address and token",
			config: &Config{
				Address: "https://vault.example.com",
				Token:   "test-token",
			},
			expectError: false,
		},
		{
			name: "missing address",
			config: &Config{
				Token: "test-token",
			},
			expectError: true,
			errorMsg:    "vault address is required",
		},
		{
			name: "missing token",
			config: &Config{
				Address: "https://vault.example.com",
			},
			expectError: true,
			errorMsg:    "vault token is required",
		},
		{
			name: "empty address",
			config: &Config{
				Address: "",
				Token:   "test-token",
			},
			expectError: true,
			errorMsg:    "vault address is required",
		},
		{
			name: "empty token",
			config: &Config{
				Address: "https://vault.example.com",
				Token:   "",
			},
			expectError: true,
			errorMsg:    "vault token is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := NewClient(tt.config)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, client)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
				assert.NotNil(t, client.client)
			}
		})
	}
}

func TestGetSecretValue(t *testing.T) {
	// Create a mock client (this would normally require a real Vault server for integration tests)
	client := &Client{
		client: nil, // In a real test, this would be a mock or test Vault client
	}

	t.Run("empty path", func(t *testing.T) {
		value, err := client.GetSecretValue("", "key")
		assert.Error(t, err)
		assert.Empty(t, value)
		assert.Contains(t, err.Error(), "secret path cannot be empty")
	})

	// Note: Additional tests would require either:
	// 1. A mock Vault server
	// 2. Integration tests with a real Vault instance
	// 3. Dependency injection to mock the underlying Vault client
}

func TestConfig(t *testing.T) {
	t.Run("config struct fields", func(t *testing.T) {
		config := &Config{
			Address: "https://vault.example.com",
			Token:   "test-token",
		}

		assert.Equal(t, "https://vault.example.com", config.Address)
		assert.Equal(t, "test-token", config.Token)
	})
}
