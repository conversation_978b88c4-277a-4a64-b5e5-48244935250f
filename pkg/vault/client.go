// Package vault provides HashiCorp Vault integration for managing secrets
package vault

import (
	"crypto/tls"
	"net/http"

	"git.homecredit.net/country/vn/platform/pulse/pkg/errors"
	"github.com/hashicorp/vault/api"
)

// Client wraps the Vault API client
type Client struct {
	client *api.Client
}

// Config holds Vault client configuration
type Config struct {
	Address       string // Vault server address
	Token         string // Vault authentication token
	TLSSkipVerify bool   // Skip TLS certificate verification
}

// NewClient creates a new Vault client with the provided configuration
func NewClient(config *Config) (*Client, error) {
	if config == nil {
		return nil, errors.New("vault config is required")
	}

	if config.Address == "" {
		return nil, errors.New("vault address is required")
	}

	if config.Token == "" {
		return nil, errors.New("vault token is required")
	}

	// Create Vault API client
	vaultConfig := api.DefaultConfig()
	vaultConfig.Address = config.Address

	// Configure TLS settings
	if config.TLSSkipVerify {
		vaultConfig.HttpClient.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
	}

	client, err := api.NewClient(vaultConfig)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create vault client")
	}

	// Set authentication token
	client.SetToken(config.Token)

	return &Client{client: client}, nil
}

// GetSecret retrieves a secret from the specified path
func (c *Client) GetSecret(path string) (*api.Secret, error) {
	if path == "" {
		return nil, errors.New("secret path cannot be empty")
	}

	secret, err := c.client.Logical().Read(path)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to read secret from path: %s", path)
	}

	if secret == nil {
		return nil, errors.Newf("secret not found at path: %s", path)
	}

	return secret, nil
}

// GetSecretData retrieves the data field from a secret at the specified path
func (c *Client) GetSecretData(path string) (map[string]interface{}, error) {
	secret, err := c.GetSecret(path)
	if err != nil {
		return nil, err
	}

	// Handle KV v2 secrets (data is nested under "data" field)
	if data, ok := secret.Data["data"]; ok {
		if dataMap, ok := data.(map[string]interface{}); ok {
			return dataMap, nil
		}
	}

	// Handle KV v1 secrets or other secret engines (data is at root level)
	return secret.Data, nil
}

// GetSecretValue retrieves a specific value from a secret using the data key
func (c *Client) GetSecretValue(path, dataKey string) (string, error) {
	data, err := c.GetSecretData(path)
	if err != nil {
		return "", err
	}

	value, exists := data[dataKey]
	if !exists {
		return "", errors.Newf("data key '%s' not found in secret at path: %s", dataKey, path)
	}

	strValue, ok := value.(string)
	if !ok {
		return "", errors.Newf("data key '%s' is not a string value in secret at path: %s", dataKey, path)
	}

	return strValue, nil
}

// Health checks if the Vault client can connect and authenticate
func (c *Client) Health() error {
	// Try to read the sys/health endpoint
	_, err := c.client.Sys().Health()
	if err != nil {
		return errors.Wrapf(err, "vault health check failed")
	}

	// Verify token is valid by trying to look up self
	_, err = c.client.Auth().Token().LookupSelf()
	if err != nil {
		return errors.Wrapf(err, "vault token validation failed")
	}

	return nil
}
