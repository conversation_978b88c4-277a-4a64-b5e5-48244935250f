package dns

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestValidateZone(t *testing.T) {
	tests := []struct {
		name     string
		zone     string
		wantErr  bool
		expected error
	}{
		{"valid zone", "example.com", false, nil},
		{"empty zone", "", true, ErrInvalidZone},
		{"zone with spaces", "example .com", true, ErrInvalidZone},
		{"zone with tabs", "example\t.com", true, ErrInvalidZone},
		{"zone with newlines", "example\n.com", true, ErrInvalidZone},
		{"whitespace only", "   ", true, ErrInvalidZone},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateZone(tt.zone)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateZone() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expected != nil && !errors.Is(err, tt.expected) {
				t.Errorf("validateZone() error = %v, expected %v", err, tt.expected)
			}
		})
	}
}

func TestParseRecord(t *testing.T) {
	tests := []struct {
		name    string
		rec     []interface{}
		want    Record
		wantErr bool
	}{
		{
			name: "valid record",
			rec:  []interface{}{"example.com", "300", "IN", "A", "192.168.1.1"},
			want: Record{
				Name:  "example.com",
				TTL:   300,
				Class: "IN",
				Type:  "A",
				Value: "192.168.1.1",
			},
			wantErr: false,
		},
		{
			name:    "insufficient fields",
			rec:     []interface{}{"example.com", "300", "IN"},
			want:    Record{},
			wantErr: true,
		},
		{
			name:    "invalid TTL",
			rec:     []interface{}{"example.com", "invalid", "IN", "A", "192.168.1.1"},
			want:    Record{},
			wantErr: true,
		},
		{
			name:    "negative TTL",
			rec:     []interface{}{"example.com", "-100", "IN", "A", "192.168.1.1"},
			want:    Record{},
			wantErr: true,
		},
		{
			name:    "non-string name",
			rec:     []interface{}{123, "300", "IN", "A", "192.168.1.1"},
			want:    Record{},
			wantErr: true,
		},
		{
			name:    "invalid record type",
			rec:     []interface{}{"example.com", "300", "IN", "INVALID", "192.168.1.1"},
			want:    Record{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseRecord(tt.rec)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got != tt.want {
				t.Errorf("parseRecord() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLogin(t *testing.T) {
	// Test server that returns cookies
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Set a test cookie
		http.SetCookie(w, &http.Cookie{
			Name:  "session",
			Value: "test-session-id",
		})

		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`{"status": "success"}`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	tests := []struct {
		name    string
		url     string
		token   string
		wantErr bool
	}{
		{"valid login", server.URL, "valid-token", false},
		{"empty url", "", "valid-token", true},
		{"empty token", server.URL, "", true},
		{"whitespace url", "   ", "valid-token", true},
		{"whitespace token", server.URL, "   ", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			dns, err := Login(ctx, tt.url, tt.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("Login() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && dns == nil {
				t.Error("Login() returned nil DNS server")
			}
		})
	}
}

func TestListRecords(t *testing.T) {
	// Test server that returns DNS records
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Return mock DNS records
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`[
			["example.com", "300", "IN", "A", "192.168.1.1"],
			["test.example.com", "600", "IN", "CNAME", "example.com"]
		]`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	// Create DNS server with mock HTTP client
	dnsServer := &DNSServer{
		Cookie: []*http.Cookie{
			{Name: "session", Value: "test"},
		},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}

	ctx := context.Background()
	records, err := dnsServer.ListRecords(ctx, "example.com")

	assert.NoError(t, err)
	assert.Len(t, records, 2)
	assert.Equal(t, "example.com", records[0].Name)
	assert.Equal(t, "A", records[0].Type)
	assert.Equal(t, "192.168.1.1", records[0].Value)
}

func TestListRecordsWithInvalidZone(t *testing.T) {
	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    "http://test.com",
	}

	ctx := context.Background()
	_, err := dnsServer.ListRecords(ctx, "")
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrInvalidZone)
}

func TestCheckResponse(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		wantErr    bool
	}{
		{"success", http.StatusOK, false},
		{"created", http.StatusCreated, true},
		{"bad request", http.StatusBadRequest, true},
		{"unauthorized", http.StatusUnauthorized, true},
		{"internal server error", http.StatusInternalServerError, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &http.Response{
				StatusCode: tt.statusCode,
				Body:       http.NoBody,
			}

			err := checkResponse(resp)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewManager(t *testing.T) {
	// Create temporary directory for tests
	tmpDir, err := os.MkdirTemp("", "dns-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	tests := []struct {
		name    string
		dir     string
		wantErr bool
	}{
		{"valid directory", tmpDir, false},
		{"non-existent directory", filepath.Join(tmpDir, "nonexistent"), false}, // Should create it
		{"empty path with HOME set", "", false},                                 // Should use HOME/.autopl/dns-backups
	}

	// Set HOME environment variable for the empty path test
	originalHome := os.Getenv("HOME")
	defer func() {
		if err := os.Setenv("HOME", originalHome); err != nil {
			t.Errorf("Failed to restore HOME: %v", err)
		}
	}()
	if err := os.Setenv("HOME", tmpDir); err != nil {
		t.Fatalf("Failed to set HOME: %v", err)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manager, err := NewManager(tt.dir)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewManager() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && manager == nil {
				t.Error("NewManager() returned nil manager")
			}
		})
	}
}

func BenchmarkParseRecord(b *testing.B) {
	rec := []interface{}{"example.com", "300", "IN", "A", "192.168.1.1"}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = parseRecord(rec)
	}
}

func BenchmarkValidateZone(b *testing.B) {
	zone := "example.com"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = validateZone(zone)
	}
}

func TestManager_Backup(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir, err := os.MkdirTemp("", "dns-backup-test")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	// Create manager
	manager, err := NewManager(tmpDir)
	assert.NoError(t, err)

	// Create a mock DNS server that returns test records
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`[
			["example.com", "300", "IN", "A", "192.168.1.1"]
		]`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}
	manager.SetDNSServer(dnsServer)

	// Test backup
	err = manager.Backup("example.com")
	assert.NoError(t, err)

	// Verify backup file exists with the correct pattern
	backupFiles, err := filepath.Glob(filepath.Join(tmpDir, "dns-backup-example.com-*.json"))
	assert.NoError(t, err)
	assert.Len(t, backupFiles, 1)
}

func TestManager_Restore(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir, err := os.MkdirTemp("", "dns-restore-test")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	// Create manager
	manager, err := NewManager(tmpDir)
	assert.NoError(t, err)

	// Create backup file with the correct naming convention
	backupData := `[
		{"name":"test.example.com","type":"A","value":"1.1.1.1","ttl":300,"class":"IN"}
	]`
	backupFile := filepath.Join(tmpDir, "dns-backup-example.com-2024-03-20.json")
	err = os.WriteFile(backupFile, []byte(backupData), 0644)
	assert.NoError(t, err)

	// Create a mock DNS server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`[]`)); err != nil { // No current records
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}
	manager.SetDNSServer(dnsServer)

	// Test restore with dry run
	err = manager.Restore("example.com", "2024-03-20", true)
	assert.NoError(t, err)
}

func TestManager_Restore_DryRun(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir, err := os.MkdirTemp("", "dns-restore-test")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	// Create backup file with the correct naming convention
	backupData := `[
		{"name":"test.example.com","type":"A","value":"1.1.1.1","ttl":300,"class":"IN"}
	]`
	backupFile := filepath.Join(tmpDir, "dns-backup-example.com-2024-03-20.json")
	err = os.WriteFile(backupFile, []byte(backupData), 0644)
	assert.NoError(t, err)

	// Create manager
	manager, err := NewManager(tmpDir)
	assert.NoError(t, err)

	// Create a mock DNS server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`[]`)); err != nil { // No current records
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}
	manager.SetDNSServer(dnsServer)

	// Test restore with dry run
	err = manager.Restore("example.com", "2024-03-20", true)
	assert.NoError(t, err)
}

func TestSetRecord(t *testing.T) {
	// Create a mock DNS server that responds correctly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		err := r.ParseForm()
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// Check for expected form fields that the actual implementation sends
		action := r.FormValue("action")
		record := r.FormValue("record")
		newValue := r.FormValue("new_value")
		recordType := r.FormValue("type")
		ttl := r.FormValue("ttl")

		if action != "replace_record" || record == "" || newValue == "" || recordType == "" || ttl == "" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`{"status": "success"}`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}

	ctx := context.Background()
	err := dnsServer.SetRecord(ctx, "test.example.com", "1.1.1.1", "A", 300)
	assert.NoError(t, err)

	// Test with invalid inputs
	err = dnsServer.SetRecord(ctx, "", "1.1.1.1", "A", 300)
	assert.Error(t, err)

	err = dnsServer.SetRecord(ctx, "test.example.com", "", "A", 300)
	assert.Error(t, err)

	err = dnsServer.SetRecord(ctx, "test.example.com", "1.1.1.1", "", 300)
	assert.Error(t, err)
}

func TestReplaceRecord(t *testing.T) {
	// Create a mock DNS server that responds correctly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		err := r.ParseForm()
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// Check for expected form fields
		action := r.FormValue("action")
		record := r.FormValue("record")
		oldValue := r.FormValue("value")
		newValue := r.FormValue("new_value")
		recordType := r.FormValue("type")
		ttl := r.FormValue("ttl")

		if action != "replace_record" || record == "" || newValue == "" || recordType == "" || ttl == "" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// For this test, we expect the old value to be specified
		if oldValue != "2.2.2.2" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`{"status": "success"}`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}

	ctx := context.Background()
	err := dnsServer.ReplaceRecord(ctx, "test.example.com", "2.2.2.2", "1.1.1.1", "A", 300)
	assert.NoError(t, err)

	// Test with invalid inputs
	err = dnsServer.ReplaceRecord(ctx, "", "2.2.2.2", "1.1.1.1", "A", 300)
	assert.Error(t, err)

	err = dnsServer.ReplaceRecord(ctx, "test.example.com", "", "", "A", 300)
	assert.Error(t, err)

	err = dnsServer.ReplaceRecord(ctx, "test.example.com", "2.2.2.2", "1.1.1.1", "", 300)
	assert.Error(t, err)
}

func TestBackupAndRestore(t *testing.T) {
	// Create temporary directory for tests
	tmpDir, err := os.MkdirTemp("", "dns-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	// Create manager
	manager, err := NewManager(tmpDir)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Create a mock DNS server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte(`[
			["test.example.com", "300", "IN", "A", "1.1.1.1"]
		]`)); err != nil {
			t.Errorf("Failed to write response: %v", err)
		}
	}))
	defer server.Close()

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    server.URL,
	}
	manager.SetDNSServer(dnsServer)

	// Test backup
	err = manager.Backup("example.com")
	if err != nil {
		t.Fatalf("Backup() error = %v", err)
	}

	// Test restore with dry run
	date := utils.NowInVietnam().Format("2006-01-02")
	err = manager.Restore("example.com", date, true)
	if err != nil {
		t.Fatalf("Restore() error = %v", err)
	}
}

func TestPrepareBackupDirectory(t *testing.T) {
	// Create temporary directory for tests
	tmpDir, err := os.MkdirTemp("", "backup-prepare-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	backupDir := filepath.Join(tmpDir, "backups")
	manager := &Manager{backupDir: backupDir}

	// Test creating backup directory
	err = manager.prepareBackupDirectory()
	assert.NoError(t, err)

	// Verify directory was created
	info, err := os.Stat(backupDir)
	assert.NoError(t, err)
	assert.True(t, info.IsDir())

	// Create some temporary files to test cleanup
	tempFiles := []string{
		filepath.Join(backupDir, "temp.tmp"),
		filepath.Join(backupDir, ".hidden"),
		filepath.Join(backupDir, "backup.partial"),
		filepath.Join(backupDir, "dns-backup-test-2024-13-40.json"), // Invalid date that will be removed
	}

	for _, file := range tempFiles {
		err := os.WriteFile(file, []byte("test"), 0644)
		assert.NoError(t, err)
	}

	// Test cleanup
	err = manager.prepareBackupDirectory()
	assert.NoError(t, err)

	// Verify temporary files were removed
	for _, file := range tempFiles {
		_, err := os.Stat(file)
		assert.True(t, os.IsNotExist(err), "Temporary file should be removed: %s", file)
	}
}

func TestWriteBackupFileAtomic(t *testing.T) {
	// Create temporary directory for tests
	tmpDir, err := os.MkdirTemp("", "atomic-write-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Errorf("Failed to remove temp dir: %v", err)
		}
	}()

	manager := &Manager{backupDir: tmpDir}
	filename := filepath.Join(tmpDir, "test-backup.json")
	testData := []byte(`{"test": "data"}`)

	// Test atomic write
	err = manager.writeBackupFileAtomic(filename, testData)
	assert.NoError(t, err)

	// Verify file was created with correct content
	content, err := os.ReadFile(filename)
	assert.NoError(t, err)
	assert.Equal(t, testData, content)

	// Verify no temporary file remains
	tempFile := filename + ".tmp"
	_, err = os.Stat(tempFile)
	assert.True(t, os.IsNotExist(err), "Temporary file should not exist after successful write")
}

func TestManager_RestoreFromCluster(t *testing.T) {
	// TestRestoreFromCluster_Comprehensive covers all essential restore scenarios
	TestRestoreFromCluster_Comprehensive(t)
}

func TestRestoreFromCluster_Comprehensive(t *testing.T) {
	tmpDir := setupTestDir(t)
	defer os.RemoveAll(tmpDir)

	tests := []struct {
		name          string
		setup         func(t *testing.T) (manager *Manager, server *httptest.Server)
		cluster       string
		zone          string
		date          string
		dryRun        bool
		expectError   bool
		errorContains string
	}{
		{
			name: "Successful restore",
			setup: func(t *testing.T) (*Manager, *httptest.Server) {
				return setupSuccessfulRestore(t, tmpDir)
			},
			cluster:     "test-cluster",
			zone:        "example.com",
			date:        "2024-01-01",
			dryRun:      true,
			expectError: false,
		},
		{
			name: "Missing cluster",
			setup: func(t *testing.T) (*Manager, *httptest.Server) {
				return setupMinimalTest(t, tmpDir)
			},
			cluster:       "nonexistent",
			zone:          "example.com",
			date:          "2024-01-01",
			dryRun:        true,
			expectError:   true,
			errorContains: "cluster 'nonexistent' not found",
		},
		{
			name: "Invalid inputs",
			setup: func(t *testing.T) (*Manager, *httptest.Server) {
				return setupMinimalTest(t, tmpDir)
			},
			cluster:       "",
			zone:          "example.com",
			date:          "2024-01-01",
			dryRun:        true,
			expectError:   true,
			errorContains: "cluster name cannot be empty",
		},
		{
			name: "Missing backup file",
			setup: func(t *testing.T) (*Manager, *httptest.Server) {
				return setupMinimalTest(t, tmpDir)
			},
			cluster:       "test-cluster",
			zone:          "missing.com",
			date:          "2024-01-01",
			dryRun:        true,
			expectError:   true,
			errorContains: "no backup found",
		},
		{
			name: "DNS server error",
			setup: func(t *testing.T) (*Manager, *httptest.Server) {
				return setupDNSErrorTest(t, tmpDir)
			},
			cluster:       "test-cluster",
			zone:          "example.com",
			date:          "2024-01-01",
			dryRun:        true,
			expectError:   true,
			errorContains: "failed to list current DNS records",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manager, server := tt.setup(t)
			if server != nil {
				defer server.Close()
			}

			err := manager.RestoreFromCluster(tt.cluster, tt.zone, tt.date, tt.dryRun)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestRestoreFromCluster_GitIntegration tests different restore scenarios
func TestRestoreFromCluster_Scenarios(t *testing.T) {
	tmpDir := setupTestDir(t)
	defer os.RemoveAll(tmpDir)

	t.Run("WithoutGit", func(t *testing.T) {
		manager, server := setupRestoreWithoutGit(t, tmpDir)
		defer server.Close()

		err := manager.RestoreFromCluster("test-cluster", "example.com", "2024-01-01", true)
		assert.NoError(t, err)
	})

	t.Run("MissingBackupFile", func(t *testing.T) {
		manager, _ := setupMinimalTest(t, tmpDir)

		err := manager.RestoreFromCluster("test-cluster", "missing.com", "2024-01-01", true)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no backup found")
	})
}

// TestRestoreFromCluster_DeleteExtraRecords tests the delete_extra_records functionality
func TestRestoreFromCluster_DeleteExtraRecords(t *testing.T) {
	tmpDir := setupTestDir(t)
	defer os.RemoveAll(tmpDir)

	tests := []struct {
		name               string
		deleteExtraRecords bool
		expectDelete       bool
		expectWarning      bool
	}{
		{
			name:               "DeleteExtraRecords_False_Default",
			deleteExtraRecords: false,
			expectDelete:       false,
			expectWarning:      true,
		},
		{
			name:               "DeleteExtraRecords_True",
			deleteExtraRecords: true,
			expectDelete:       true,
			expectWarning:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create backup file (only contains one record)
			backupRecords := []Record{
				{Name: "keep.example.com", Type: "A", Value: "1.1.1.1", TTL: 300, Class: "IN"},
			}
			createBackupFile(t, tmpDir, "example.com", "2024-01-01", backupRecords)

			// Create mock DNS server that has an extra record
			var deleteRequested bool
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				switch r.FormValue("action") {
				case "list_records":
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusOK)
					// Return records: one that matches backup + one extra
					json.NewEncoder(w).Encode([][]interface{}{
						{"keep.example.com", "300", "IN", "A", "1.1.1.1"},  // Matches backup
						{"extra.example.com", "300", "IN", "A", "2.2.2.2"}, // Extra record
					})
				case "delete_record":
					deleteRequested = true
					w.WriteHeader(http.StatusOK)
				default:
					w.WriteHeader(http.StatusBadRequest)
				}
			}))
			defer server.Close()

			// Create config with the delete_extra_records setting
			configContent := fmt.Sprintf(`
dns:
  test-cluster:
    url: %s
    backup_dir: %s
    retention_days: 30
    delete_extra_records: %t
`, server.URL, tmpDir, tt.deleteExtraRecords)

			configFile := filepath.Join(tmpDir, "pulse.yaml")
			os.WriteFile(configFile, []byte(configContent), 0644)
			setupViperConfig(configFile)

			manager, _ := NewManager(tmpDir)
			dnsServer := &DNSServer{
				Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
				httpClient: &http.Client{},
				baseURL:    server.URL,
			}
			manager.SetDNSServer(dnsServer)

			// Test restore operation (non-dry-run to trigger actual delete calls)
			err := manager.RestoreFromCluster("test-cluster", "example.com", "2024-01-01", false)
			assert.NoError(t, err)

			// Verify delete behavior
			if tt.expectDelete {
				assert.True(t, deleteRequested, "Expected delete to be requested when delete_extra_records=true")
			} else {
				assert.False(t, deleteRequested, "Expected no delete request when delete_extra_records=false")
			}
		})
	}
}

// Helper functions for test setup
func setupTestDir(t *testing.T) string {
	tmpDir, err := os.MkdirTemp("", "dns-test")
	if err != nil {
		t.Fatal(err)
	}
	return tmpDir
}

func setupSuccessfulRestore(t *testing.T, tmpDir string) (*Manager, *httptest.Server) {
	// Create backup file
	backupRecords := []Record{
		{Name: "test.example.com", Type: "A", Value: "1.1.1.1", TTL: 300, Class: "IN"},
		{Name: "api.example.com", Type: "A", Value: "2.2.2.2", TTL: 600, Class: "IN"},
	}
	createBackupFile(t, tmpDir, "example.com", "2024-01-01", backupRecords)

	// Create mock DNS server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.FormValue("action") {
		case "list_records":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			// Return different records to test UPDATE scenario
			json.NewEncoder(w).Encode([][]interface{}{
				{"test.example.com", "300", "IN", "A", "9.9.9.9"}, // Different value
			})
		case "replace_record":
			w.WriteHeader(http.StatusOK)
		default:
			w.WriteHeader(http.StatusBadRequest)
		}
	}))

	manager := createTestManager(t, tmpDir, server.URL)
	return manager, server
}

func setupMinimalTest(t *testing.T, tmpDir string) (*Manager, *httptest.Server) {
	createTestConfig(t, tmpDir, "http://example.com")
	manager, _ := NewManager(tmpDir)
	return manager, nil
}

func setupDNSErrorTest(t *testing.T, tmpDir string) (*Manager, *httptest.Server) {
	// Create backup file
	backupRecords := []Record{
		{Name: "test.example.com", Type: "A", Value: "1.1.1.1", TTL: 300, Class: "IN"},
	}
	createBackupFile(t, tmpDir, "example.com", "2024-01-01", backupRecords)

	// Create DNS server that returns errors
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Server error"))
	}))

	manager := createTestManager(t, tmpDir, server.URL)
	return manager, server
}

func setupRestoreWithoutGit(t *testing.T, tmpDir string) (*Manager, *httptest.Server) {
	backupRecords := []Record{
		{Name: "local.example.com", Type: "A", Value: "1.1.1.1", TTL: 300, Class: "IN"},
	}
	createBackupFile(t, tmpDir, "example.com", "2024-01-01", backupRecords)

	server := createMockDNSServer()
	manager := createTestManagerWithoutGit(t, tmpDir, server.URL)
	return manager, server
}

func createBackupFile(t *testing.T, tmpDir, zone, date string, records []Record) {
	backupData, _ := json.MarshalIndent(records, "", "  ")
	backupFile := filepath.Join(tmpDir, fmt.Sprintf("dns-backup-%s-%s.json", zone, date))
	err := os.WriteFile(backupFile, backupData, 0644)
	assert.NoError(t, err)
}

func createTestManager(t *testing.T, tmpDir, serverURL string) *Manager {
	createTestConfig(t, tmpDir, serverURL)
	manager, err := NewManager(tmpDir)
	assert.NoError(t, err)

	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    serverURL,
	}
	manager.SetDNSServer(dnsServer)
	return manager
}

func createTestManagerWithoutGit(t *testing.T, tmpDir, serverURL string) *Manager {
	configContent := fmt.Sprintf(`
dns:
  test-cluster:
    url: %s
    backup_dir: %s
    retention_days: 30
`, serverURL, tmpDir)

	configFile := filepath.Join(tmpDir, "pulse.yaml")
	os.WriteFile(configFile, []byte(configContent), 0644)
	setupViperConfig(configFile)

	manager, _ := NewManager(tmpDir)
	dnsServer := &DNSServer{
		Cookie:     []*http.Cookie{{Name: "session", Value: "test"}},
		httpClient: &http.Client{},
		baseURL:    serverURL,
	}
	manager.SetDNSServer(dnsServer)
	return manager
}

func createTestConfig(t *testing.T, tmpDir, serverURL string) {
	configContent := fmt.Sprintf(`
dns:
  test-cluster:
    url: %s
    backup_dir: %s
    retention_days: 30
`, serverURL, tmpDir)

	configFile := filepath.Join(tmpDir, "pulse.yaml")
	os.WriteFile(configFile, []byte(configContent), 0644)
	setupViperConfig(configFile)
}

func setupViperConfig(configFile string) {
	viper.Reset()
	config.Reset()
	viper.SetConfigFile(configFile)
	config.Reset()
}

func createMockDNSServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.FormValue("action") {
		case "list_records":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`[]`))
		case "replace_record":
			w.WriteHeader(http.StatusOK)
		default:
			w.WriteHeader(http.StatusBadRequest)
		}
	}))
}
