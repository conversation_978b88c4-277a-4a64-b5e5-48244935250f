// Package dns provides DNS record management functionality including
// backup, restore, and cleanup operations for DNS records.
package dns

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	neturl "net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"gopkg.in/yaml.v3"
	"git.homecredit.net/country/vn/platform/pulse/internal/utils"
	"git.homecredit.net/country/vn/platform/pulse/pkg/config"
	"git.homecredit.net/country/vn/platform/pulse/pkg/logger"
)

// DNS-specific errors
var (
	ErrUnsupportedFormat = errors.New("unsupported format")
	ErrBackupNotFound    = errors.New("backup file not found")
	ErrInvalidZone       = errors.New("invalid zone name")
	ErrEmptyResponse     = errors.New("empty response from server")
	ErrInvalidTTL        = errors.New("invalid TTL value")
	ErrInvalidRecord     = errors.New("invalid record format")
	ErrInvalidRecordType = errors.New("invalid record type")
	ErrInvalidInput      = errors.New("invalid input")
	ErrAuthentication    = errors.New("authentication failed")
)

// Constants
const (
	defaultTimeout     = 30 * time.Second
	defaultIdleTimeout = 90 * time.Second
	maxResponseSize    = 4096
	defaultTTL         = 300        // 5 minutes default TTL instead of 60 seconds
	maxTTL             = 2147483647 // Max 32-bit signed integer
	minTTL             = 0          // Minimum TTL for DNS records
)

// Regular expressions for input validation
var (
	// Valid zone name pattern (allows dots, hyphens, alphanumeric)
	zoneNameRegex = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-\.]*[a-zA-Z0-9])?$`)
	// Valid record name pattern (allows wildcards, dots, hyphens, alphanumeric)
	recordNameRegex = regexp.MustCompile(`^(\*\.)?[a-zA-Z0-9]([a-zA-Z0-9\-\.]*[a-zA-Z0-9])?$`)
)

// Record represents a DNS record
type Record struct {
	Name  string `json:"name" yaml:"name"` 
	Type  string `json:"type" yaml:"type"`
	Value string `json:"value" yaml:"value"`
	TTL   int    `json:"ttl" yaml:"ttl"`
	Class string `json:"class" yaml:"class"` // Typically "IN" for Internet
}

// HTTPClient interface for testing and flexibility
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// DNSServer represents a DNS server connection
type DNSServer struct {
	Cookie     []*http.Cookie
	httpClient HTTPClient
	baseURL    string
	mu         sync.RWMutex // protects concurrent access to cookies
}

// defaultHTTPClient creates an optimized HTTP client with connection pooling
var defaultHTTPClient = &http.Client{
	Timeout: defaultTimeout,
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     defaultIdleTimeout,
		ForceAttemptHTTP2:   true,
	},
}

// ArchAppConfig represents the architecture application configuration
type ArchAppConfig struct {
    Apps []struct {
        AppCode            string `yaml:"app_code"`
        AppName            string `yaml:"app_name"`
        TeamEmail          string `yaml:"team_email"`
        ITOwnerEmail       string `yaml:"it_owner_email"`
        BusinessOwnerEmail string `yaml:"business_owner_email"`
        Description        string `yaml:"description"`
    } `yaml:"apps"`
}
// validateLoginInputs validates login parameters
func validateLoginInputs(url, token string) error {
	if strings.TrimSpace(url) == "" {
		return fmt.Errorf("%w: URL cannot be empty", ErrInvalidInput)
	}
	if strings.TrimSpace(token) == "" {
		return fmt.Errorf("%w: token cannot be empty", ErrInvalidInput)
	}
	return nil
}

// Login logs in to the DNS server with context support
func Login(ctx context.Context, url, token string) (*DNSServer, error) {
	if err := validateLoginInputs(url, token); err != nil {
		return nil, err
	}

	form := neturl.Values{}
	form.Add("action", "login_token")
	form.Add("token", token)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create login request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to login: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return nil, fmt.Errorf("%w: %w", ErrAuthentication, err)
	}

	cookies := resp.Cookies()
	if len(cookies) == 0 {
		return nil, fmt.Errorf("%w: no authentication cookies received", ErrAuthentication)
	}

	return &DNSServer{
		Cookie:     cookies,
		httpClient: defaultHTTPClient,
		baseURL:    url,
	}, nil
}

// validateZone validates zone name format with enhanced validation
func validateZone(zone string) error {
	zone = strings.TrimSpace(zone)
	if zone == "" {
		return fmt.Errorf("%w: zone name cannot be empty", ErrInvalidZone)
	}
	if len(zone) > 253 {
		return fmt.Errorf("%w: zone name too long (max 253 characters)", ErrInvalidZone)
	}
	if !zoneNameRegex.MatchString(zone) {
		return fmt.Errorf("%w: invalid zone name format", ErrInvalidZone)
	}
	return nil
}

// validateRecordName validates DNS record name format
func validateRecordName(name string) error {
	name = strings.TrimSpace(name)
    if strings.HasSuffix(name, ".") {
        name = strings.TrimSuffix(name, ".")
    }
	if name == "" {
		return fmt.Errorf("%w: record name cannot be empty", ErrInvalidInput)
	}
	if len(name) > 253 {
		return fmt.Errorf("%w: record name too long (max 253 characters)", ErrInvalidInput)
	}
	if !recordNameRegex.MatchString(name) {
		return fmt.Errorf("%w: invalid record name format", ErrInvalidInput)
	}
	return nil
}

// validateTTL validates TTL value with enhanced checks
func validateTTL(ttl int) error {
	if ttl < 0 {
		return fmt.Errorf("%w: TTL cannot be negative", ErrInvalidTTL)
	}
	if ttl < minTTL {
		return fmt.Errorf("%w: TTL too low (minimum %d seconds)", ErrInvalidTTL, minTTL)
	}
	if ttl > maxTTL {
		return fmt.Errorf("%w: TTL too high (maximum %d seconds)", ErrInvalidTTL, maxTTL)
	}
	return nil
}

// ListRecords lists DNS records for a zone with context support
func (s *DNSServer) ListRecords(ctx context.Context, zone string) ([]Record, error) {
	if err := validateZone(zone); err != nil {
		return nil, err
	}

	form := neturl.Values{}
	form.Add("action", "list_records")
	form.Add("zone", zone)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.baseURL, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	s.mu.RLock()
	for _, cookie := range s.Cookie {
		req.AddCookie(cookie)
	}
	s.mu.RUnlock()

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list records: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return nil, err
	}

	var rawRecords [][]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&rawRecords); err != nil {
		return nil, fmt.Errorf("failed to unmarshal records: %w", err)
	}

	if len(rawRecords) == 0 {
		return []Record{}, nil
	}

	records := make([]Record, 0, len(rawRecords))
	for i, rec := range rawRecords {
		record, err := parseRecord(rec)
		if err != nil {
			logger.Warnf("Warning: skipping invalid record at index %d: %v", i, err)
			continue
		}
		records = append(records, record)
	}

	return records, nil
}

// validRecordTypes contains all supported DNS record types for efficient lookup
var validRecordTypes = map[string]struct{}{
	"A":     {},
	"AAAA":  {},
	"CNAME": {},
	"MX":    {},
	"NS":    {},
	"PTR":   {},
	"SOA":   {},
	"SRV":   {},
	"TXT":   {},
}

// isValidRecordType checks if the record type is supported using an efficient set lookup
func isValidRecordType(rtype string) bool {
	_, exists := validRecordTypes[rtype]
	return exists
}

// parseRecord extracts and validates a DNS record from raw data
func parseRecord(rec []interface{}) (Record, error) {
	if len(rec) < 5 {
		return Record{}, fmt.Errorf("%w: expected at least 5 fields, got %d", ErrInvalidRecord, len(rec))
	}

	// Type-safe extraction with validation
	name, ok := rec[0].(string)
	if !ok {
		return Record{}, fmt.Errorf("%w: name field must be a string", ErrInvalidRecord)
	}

	ttlStr, ok := rec[1].(string)
	if !ok {
		return Record{}, fmt.Errorf("%w: TTL field must be a string", ErrInvalidRecord)
	}

	class, ok := rec[2].(string)
	if !ok {
		return Record{}, fmt.Errorf("%w: class field must be a string", ErrInvalidRecord)
	}

	rtype, ok := rec[3].(string)
	if !ok {
		return Record{}, fmt.Errorf("%w: type field must be a string", ErrInvalidRecord)
	}

	value, ok := rec[4].(string)
	if !ok {
		return Record{}, fmt.Errorf("%w: value field must be a string", ErrInvalidRecord)
	}

	// Parse TTL with better error handling
	ttl, err := strconv.Atoi(ttlStr)
	if err != nil {
		return Record{}, fmt.Errorf("%w: %v", ErrInvalidTTL, err)
	}
	if err := validateTTL(ttl); err != nil {
		return Record{}, err
	}

	// Validate record type
	if !isValidRecordType(rtype) {
		return Record{}, fmt.Errorf("%w: %s", ErrInvalidRecordType, rtype)
	}

	return Record{
		Name:  name,
		TTL:   ttl,
		Class: class,
		Type:  rtype,
		Value: value,
	}, nil
}

// checkResponse checks the response from the server
func checkResponse(resp *http.Response) error {
	if resp.StatusCode == http.StatusOK {
		return nil
	}

	body, err := io.ReadAll(io.LimitReader(resp.Body, maxResponseSize))
	if err != nil {
		return fmt.Errorf("request failed with status %d (failed to read error body: %w)", resp.StatusCode, err)
	}

	bodyStr := strings.TrimSpace(string(body))
	if bodyStr == "" {
		bodyStr = http.StatusText(resp.StatusCode)
	}

	return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, bodyStr)
}

// Manager handles DNS operations
type Manager struct {
	backupDir string
	dnsServer *DNSServer
	mu        sync.RWMutex
}

// NewManager creates a new DNS manager
func NewManager(backupDir string) (*Manager, error) {
	if backupDir == "" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get user home directory: %w", err)
		}
		backupDir = filepath.Join(homeDir, ".pulse", "dns-backups")
	}

	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create backup directory: %w", err)
	}
	return &Manager{backupDir: backupDir}, nil
}

// Backup creates a backup of DNS records for a specific zone
func (m *Manager) Backup(zone string) error {
	// Validate zone
	if err := validateZone(zone); err != nil {
		return fmt.Errorf("invalid zone: %w", err)
	}

	// Get current records from DNS server
	if m.dnsServer == nil {
		return fmt.Errorf("DNS server not configured")
	}

	records, err := m.dnsServer.ListRecords(context.Background(), zone)
	if err != nil {
		return fmt.Errorf("failed to fetch DNS records: %w", err)
	}

	// Generate filename with date suffix
	timestamp := utils.NowInVietnam().Format("2006-01-02")
	filename := filepath.Join(m.backupDir, fmt.Sprintf("dns-backup-%s-%s.json", zone, timestamp))

	// Marshal records to JSON
	data, err := json.MarshalIndent(records, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal records: %w", err)
	}

	// Write backup file atomically (write to temp file first, then rename)
	if err := m.writeBackupFileAtomic(filename, data); err != nil {
		return fmt.Errorf("failed to write backup file: %w", err)
	}

	logger.Infof("Backup created successfully: %s", filename)
	return nil
}

// writeBackupFileAtomic writes backup data to a file atomically to prevent corruption
func (m *Manager) writeBackupFileAtomic(filename string, data []byte) error {
	// Create temporary file in the same directory
	tempFile := filename + ".tmp"

	// Write data to temporary file
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temporary file: %w", err)
	}

	// Atomically rename temporary file to final filename
	if err := os.Rename(tempFile, filename); err != nil {
		// Clean up temporary file on failure
		if removeErr := os.Remove(tempFile); removeErr != nil {
			logger.Warnf("Failed to clean up temporary file: %v", removeErr)
		}
		return fmt.Errorf("failed to rename temporary file: %w", err)
	}

	return nil
}

// prepareBackupDirectory ensures the backup directory exists and is ready for new backups
func (m *Manager) prepareBackupDirectory() error {
	// Create backup directory if it doesn't exist
	if err := os.MkdirAll(m.backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// Check if directory exists and is accessible
	if info, err := os.Stat(m.backupDir); err != nil {
		return fmt.Errorf("failed to access backup directory: %w", err)
	} else if !info.IsDir() {
		return fmt.Errorf("backup path exists but is not a directory: %s", m.backupDir)
	}

	// Clean up any temporary files or incomplete backups from previous runs
	if err := m.cleanupTempFiles(); err != nil {
		logger.Warnf("Warning: failed to cleanup temporary files: %v", err)
		// Don't fail the backup for cleanup issues, just warn
	}

	logger.Debugf("Backup directory prepared: %s", m.backupDir)
	return nil
}

// cleanupTempFiles removes any temporary or incomplete backup files
func (m *Manager) cleanupTempFiles() error {
	entries, err := os.ReadDir(m.backupDir)
	if err != nil {
		return fmt.Errorf("failed to read backup directory: %w", err)
	}

	var cleanupErrors []string
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		filename := entry.Name()
		logger.Debugf("Processing cleanup for file: %s", filename)

		// Remove temporary files (files ending with .tmp, .partial, etc.)
		if strings.HasSuffix(filename, ".tmp") ||
			strings.HasSuffix(filename, ".partial") ||
			strings.HasPrefix(filename, ".") {
			path := filepath.Join(m.backupDir, filename)
			if err := os.Remove(path); err != nil {
				cleanupErrors = append(cleanupErrors, fmt.Sprintf("failed to remove temp file %s: %v", filename, err))
			} else {
				logger.Debugf("Removed temporary file: %s", filename)
			}
		}

		// Remove incomplete backup files (files that don't match our naming pattern)
		if strings.HasPrefix(filename, "dns-backup-") && strings.HasSuffix(filename, ".json") {
			// Extract the date from the filename: dns-backup-{zone}-{YYYY-MM-DD}.json
			// Zone names can contain dots, so we need to find the last occurrence of date pattern
			nameWithoutExt := strings.TrimSuffix(filename, ".json")

			// Look for date pattern at the end: -YYYY-MM-DD
			// The date part should be the last 10 characters after the last dash: -2025-06-04
			if len(nameWithoutExt) >= len("dns-backup-x-2025-06-04") {
				// Find the date part by looking for the pattern at the end
				expectedDateStart := len(nameWithoutExt) - 10 // 10 chars for YYYY-MM-DD
				logger.Debugf("File %s: nameWithoutExt='%s', length=%d, expectedDateStart=%d", filename, nameWithoutExt, len(nameWithoutExt), expectedDateStart)
				if expectedDateStart > 0 && nameWithoutExt[expectedDateStart-1] == '-' {
					dateStr := nameWithoutExt[expectedDateStart:]
					logger.Debugf("Checking date format for %s: extracted date '%s'", filename, dateStr)
					if _, err := time.Parse("2006-01-02", dateStr); err != nil {
						// Invalid date format, likely an incomplete file
						logger.Debugf("Invalid date format in %s, removing file: %v", filename, err)
						path := filepath.Join(m.backupDir, filename)
						if err := os.Remove(path); err != nil {
							cleanupErrors = append(cleanupErrors, fmt.Sprintf("failed to remove invalid backup file %s: %v", filename, err))
						} else {
							logger.Debugf("Removed invalid backup file: %s", filename)
						}
					} else {
						logger.Debugf("Valid backup file: %s", filename)
					}
				} else {
					logger.Debugf("File %s doesn't match expected pattern for date extraction", filename)
					if expectedDateStart > 0 {
						logger.Debugf("Character at position %d: '%c' (expected '-')", expectedDateStart-1, nameWithoutExt[expectedDateStart-1])
					}
				}
			}
		}
	}

	if len(cleanupErrors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %s", strings.Join(cleanupErrors, "; "))
	}

	return nil
}

// Restore restores DNS records from a backup (backward compatibility)
func (m *Manager) Restore(zone string, date string, dryRun bool) error {
	if date == "" {
		return errors.New("date must be provided")
	}

	// Look for JSON backup file
	file := filepath.Join(m.backupDir, fmt.Sprintf("dns-backup-%s-%s.json", zone, date))

	if _, err := os.Stat(file); err != nil {
		return fmt.Errorf("%w: no backup found for zone %s on date %s", ErrBackupNotFound, zone, date)
	}

	data, err := os.ReadFile(file)
	if err != nil {
		return fmt.Errorf("failed to read backup file: %w", err)
	}

	var backupRecords []Record
	if err := json.Unmarshal(data, &backupRecords); err != nil {
		return fmt.Errorf("failed to unmarshal JSON records: %w", err)
	}

	// Get current records from server
	currentRecords, err := m.ListRecords(context.Background(), zone)
	if err != nil {
		return fmt.Errorf("failed to list current records: %w", err)
	}

	// Create maps for easier comparison
	backupMap := make(map[string]Record)
	for _, r := range backupRecords {
		backupMap[r.Name] = r
	}

	currentMap := make(map[string]Record)
	for _, r := range currentRecords {
		currentMap[r.Name] = r
	}

	// Track changes
	var changes []string

	// Compare and update records
	for name, backupRec := range backupMap {
		currentRec, exists := currentMap[name]
		if !exists {
			// Record doesn't exist on server, needs to be created
			if !dryRun {
				if err := m.dnsServer.SetRecord(context.Background(), backupRec.Name, backupRec.Value, backupRec.Type, backupRec.TTL); err != nil {
					return fmt.Errorf("failed to create record %s: %w", name, err)
				}
			}
			changes = append(changes, fmt.Sprintf("+ %s (%s) > %s", name, backupRec.Type, backupRec.Value))
			continue
		}

		// Record exists, check if it needs updating
		if currentRec.Value != backupRec.Value || currentRec.Type != backupRec.Type || currentRec.TTL != backupRec.TTL {
			if !dryRun {
				// Use ReplaceRecord to specify exactly which record to replace
				if err := m.dnsServer.ReplaceRecord(context.Background(), backupRec.Name, currentRec.Value, backupRec.Value, backupRec.Type, backupRec.TTL); err != nil {
					return fmt.Errorf("failed to update record %s: %w", name, err)
				}
			}
			changes = append(changes, fmt.Sprintf("~ %s (%s) %s > %s", name, backupRec.Type, currentRec.Value, backupRec.Value))
		}
	}

	// Handle records that exist in current DNS but not in backup (delete them for complete restoration)
	for name, currentRec := range currentMap {
		if _, existsInBackup := backupMap[name]; !existsInBackup {
			// Record exists on server but not in backup, should be deleted
			if !dryRun {
				if err := m.dnsServer.DeleteRecordByValue(context.Background(), currentRec.Name, currentRec.Type, currentRec.Value); err != nil {
					logger.Warnf("Warning: failed to delete record %s (%s): %v", name, currentRec.Type, err)
					// Continue with other operations instead of failing completely
					changes = append(changes, fmt.Sprintf("⚠ %s (%s) deletion failed: %v", name, currentRec.Type, err))
					continue
				}
			}
			changes = append(changes, fmt.Sprintf("- %s (%s) %s", name, currentRec.Type, currentRec.Value))
		}
	}

	// Log changes
	if len(changes) > 0 {
		if dryRun {
			logger.Infof("Would make the following changes to zone %s:", zone)
		} else {
			logger.Infof("Made the following changes to zone %s:", zone)
		}
		for _, change := range changes {
			logger.Infof("  %s", change)
		}
	} else {
		logger.Infof("No changes needed for zone %s", zone)
	}

	return nil
}

// RestoreFromCluster restores DNS records from a backup using cluster configuration
func (m *Manager) RestoreFromCluster(cluster, zone, date string, dryRun bool) error {
	// Validate inputs
	if err := m.validateRestoreInputs(cluster, zone, date); err != nil {
		return err
	}

	// Get cluster configuration
	clusterCfg, err := config.GetCluster(cluster)
	if err != nil {
		return fmt.Errorf("failed to get cluster configuration: %w", err)
	}

	// Check backup file exists
	backupFile := filepath.Join(clusterCfg.BackupDir, fmt.Sprintf("dns-backup-%s-%s.json", zone, date))
	if _, err := os.Stat(backupFile); err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("%w: no backup found for cluster %s, zone %s on date %s at %s",
				ErrBackupNotFound, cluster, zone, date, backupFile)
		}
		return fmt.Errorf("failed to access backup file: %w", err)
	}

	// Load backup records
	backupRecords, err := m.loadBackupFile(backupFile)
	if err != nil {
		return err
	}

	// Get current DNS records
	currentRecords, err := m.getCurrentRecords(zone)
	if err != nil {
		return err
	}

	// Perform restore operations
	return m.performRestore(cluster, zone, date, backupRecords, currentRecords, clusterCfg, dryRun)
}

// validateRestoreInputs validates the input parameters
func (m *Manager) validateRestoreInputs(cluster, zone, date string) error {
	if cluster == "" {
		return fmt.Errorf("%w: cluster name cannot be empty", ErrInvalidInput)
	}
	if zone == "" {
		return fmt.Errorf("%w: zone name cannot be empty", ErrInvalidInput)
	}
	if date == "" {
		return fmt.Errorf("%w: date cannot be empty", ErrInvalidInput)
	}
	return validateZone(zone)
}

// loadBackupFile reads and parses the backup file
func (m *Manager) loadBackupFile(backupFile string) ([]Record, error) {
	data, err := os.ReadFile(backupFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read backup file %s: %w", backupFile, err)
	}

	var backupRecords []Record
	if err := json.Unmarshal(data, &backupRecords); err != nil {
		return nil, fmt.Errorf("failed to unmarshal backup records from %s: %w", backupFile, err)
	}

	logger.Infof("Loaded %d records from backup file: %s", len(backupRecords), backupFile)
	return backupRecords, nil
}

// getCurrentRecords gets current DNS records from the server
func (m *Manager) getCurrentRecords(zone string) ([]Record, error) {
	if m.dnsServer == nil {
		return nil, fmt.Errorf("DNS server not configured for restore operation")
	}

	currentRecords, err := m.dnsServer.ListRecords(context.Background(), zone)
	if err != nil {
		return nil, fmt.Errorf("failed to list current DNS records for zone %s: %w", zone, err)
	}

	logger.Infof("Found %d current records on DNS server for zone %s", len(currentRecords), zone)
	return currentRecords, nil
}

// performRestore executes the actual restore operations
func (m *Manager) performRestore(cluster, zone, date string, backupRecords, currentRecords []Record, clusterCfg *config.DNSCluster, dryRun bool) error {
	// Create lookup maps for efficient record comparison
	backupMap, currentMap := m.createRecordMaps(backupRecords, currentRecords, zone)

	// Initialize tracking structures
	var changes []string
	var warnings []string
	stats := &RestoreStats{}

	// Process records from backup (create/update)
	if err := m.processBackupRecords(backupMap, currentMap, stats, &changes, dryRun); err != nil {
		return err
	}

	// Handle extra records on server (delete/warn)
	m.processExtraRecords(currentMap, backupMap, clusterCfg, stats, &changes, &warnings, dryRun)

	// Report results
	m.reportRestoreResults(cluster, zone, date, stats, changes, warnings, dryRun)
	return nil
}

// createRecordMaps creates lookup maps for efficient record comparison
func (m *Manager) createRecordMaps(backupRecords, currentRecords []Record, zone string) (map[string]Record, map[string]Record) {
	backupMap := make(map[string]Record)
	for _, record := range backupRecords {
		record.Name = normalizeRecordName(record.Name, zone)
		backupMap[record.Name] = record
	}

	currentMap := make(map[string]Record)
	for _, record := range currentRecords {
		record.Name = normalizeRecordName(record.Name, zone)
		currentMap[record.Name] = record
	}

	return backupMap, currentMap
}

// processBackupRecords processes records from backup, creating or updating as needed
func (m *Manager) processBackupRecords(backupMap, currentMap map[string]Record, stats *RestoreStats, changes *[]string, dryRun bool) error {
	for recordName, backupRecord := range backupMap {
		stats.Processed++
		currentRecord, exists := currentMap[recordName]

		if !exists {
			// CREATE: Record missing on server
			if err := m.executeCreate(backupRecord, dryRun); err != nil {
				return fmt.Errorf("failed to create record %s: %w", recordName, err)
			}
			*changes = append(*changes, fmt.Sprintf("+ CREATE: %s (%s) → %s [TTL: %d]",
				recordName, backupRecord.Type, backupRecord.Value, backupRecord.TTL))
			stats.Created++
		} else if m.recordNeedsUpdate(currentRecord, backupRecord) {
			// UPDATE: Record changed
			if err := m.executeUpdate(currentRecord, backupRecord, dryRun); err != nil {
				return fmt.Errorf("failed to update record %s: %w", recordName, err)
			}
			*changes = append(*changes, fmt.Sprintf("~ UPDATE: %s (%s) %s → %s [TTL: %d]",
				recordName, backupRecord.Type, currentRecord.Value, backupRecord.Value, backupRecord.TTL))
			stats.Updated++
		} else {
			// KEEP: Record unchanged
			stats.Kept++
		}
	}
	return nil
}

// processExtraRecords handles records that exist on server but not in backup
func (m *Manager) processExtraRecords(currentMap, backupMap map[string]Record, clusterCfg *config.DNSCluster, stats *RestoreStats, changes, warnings *[]string, dryRun bool) {
	for recordName, currentRecord := range currentMap {
		if _, existsInBackup := backupMap[recordName]; !existsInBackup {
			if clusterCfg.DeleteExtraRecords {
				// DELETE: Record exists on server but not in backup
				if err := m.executeDelete(currentRecord, dryRun); err != nil {
					logger.Errorf("Failed to delete extra record %s: %v", recordName, err)
					continue
				}
				*changes = append(*changes, fmt.Sprintf("- DELETE: %s (%s) %s [TTL: %d]",
					recordName, currentRecord.Type, currentRecord.Value, currentRecord.TTL))
				stats.Deleted++
			} else {
				// WARNING: Record exists on server but not in backup (default behavior)
				warningMsg := fmt.Sprintf("⚠ WARNING: Record exists on server but not in backup: %s (%s) = %s [TTL: %d]",
					recordName, currentRecord.Type, currentRecord.Value, currentRecord.TTL)
				*warnings = append(*warnings, warningMsg)
				stats.Warnings++
			}
		}
	}
}

// RestoreStats holds statistics about the restore operation
type RestoreStats struct {
	Processed int
	Created   int
	Updated   int
	Kept      int
	Deleted   int
	Warnings  int
}

// recordNeedsUpdate checks if a record needs to be updated
func (m *Manager) recordNeedsUpdate(current, backup Record) bool {
	return current.Value != backup.Value || current.Type != backup.Type || current.TTL != backup.TTL
}

// executeCreate creates a new DNS record
func (m *Manager) executeCreate(record Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.SetRecord(context.Background(), record.Name, record.Value, record.Type, record.TTL)
	}
	return nil
}

// executeUpdate updates an existing DNS record
func (m *Manager) executeUpdate(current, backup Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.ReplaceRecord(context.Background(), backup.Name, current.Value, backup.Value, backup.Type, backup.TTL)
	}
	return nil
}

// executeDelete deletes a DNS record
func (m *Manager) executeDelete(record Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.DeleteRecord(context.Background(), record.Name, record.Type, true)
	}
	return nil
}

// reportRestoreResults logs the results of the restore operation
func (m *Manager) reportRestoreResults(cluster, zone, date string, stats *RestoreStats, changes, warnings []string, dryRun bool) {
	logger.Infof("Restore operation completed for cluster %s, zone %s, date %s:", cluster, zone, date)
	logger.Infof("  Total records processed: %d", stats.Processed)
	logger.Infof("  Records created: %d", stats.Created)
	logger.Infof("  Records updated: %d", stats.Updated)
	logger.Infof("  Records unchanged (kept): %d", stats.Kept)
	if stats.Deleted > 0 {
		logger.Infof("  Records deleted: %d", stats.Deleted)
	}
	logger.Infof("  Extra records on server: %d", stats.Warnings)

	if len(changes) > 0 {
		if dryRun {
			logger.Infof("Would make the following changes to zone %s:", zone)
		} else {
			logger.Infof("Made the following changes to zone %s:", zone)
		}
		for _, change := range changes {
			logger.Infof("  %s", change)
		}
	}

	if len(warnings) > 0 {
		logger.Infof("Extra records found on server (not in backup):")
		for _, warning := range warnings {
			logger.Infof("  %s", warning)
		}
		logger.Infof("Note: Extra records were left unchanged. You may want to review these manually.")
	}

	if len(changes) == 0 && len(warnings) == 0 {
		logger.Infof("No changes needed for zone %s - DNS records match backup perfectly", zone)
	}
}

// PrepareForBackup ensures the backup directory is ready for new backups
func (m *Manager) PrepareForBackup() error {
	return m.prepareBackupDirectory()
}

// SetRecord replaces a DNS record by specifying the old value to replace with a new value
func (s *DNSServer) SetRecord(ctx context.Context, record, newValue, recordType string, ttl ...int) error {
	return s.ReplaceRecord(ctx, record, "", newValue, recordType, ttl...)
}

// DeleteRecord deletes a DNS record with proper validation and error handling
func (s *DNSServer) DeleteRecord(ctx context.Context, record, recordType string, withPtr ...bool) error {
	if err := validateRecordName(record); err != nil {
		return err
	}

	if !isValidRecordType(recordType) {
		return fmt.Errorf("%w: %s", ErrInvalidRecordType, recordType)
	}

	// Determine if PTR record should be deleted as well (default: true for non-wildcard records)
	deletePTR := !strings.Contains(record, "*") // Default behavior
	if len(withPtr) > 0 {
		deletePTR = withPtr[0] // Override with explicit parameter
	}

	form := neturl.Values{}
	form.Add("action", "delete_record")
	form.Add("record", record)
	form.Add("type", recordType)

	if deletePTR {
		form.Add("ptr", "true")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.baseURL, strings.NewReader(form.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	s.mu.RLock()
	for _, cookie := range s.Cookie {
		req.AddCookie(cookie)
	}
	s.mu.RUnlock()

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete record: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return fmt.Errorf("failed to delete record: %w", err)
	}

	logger.Infof("Successfully deleted DNS record %s (%s)%s", record, recordType,
		func() string {
			if deletePTR {
				return " with PTR record"
			}
			return ""
		}())

	return nil
}

// DeleteRecordByValue deletes a specific DNS record by its value (for cases with multiple records of same type)
func (s *DNSServer) DeleteRecordByValue(ctx context.Context, record, recordType, value string, withPtr ...bool) error {
	if err := validateRecordName(record); err != nil {
		return err
	}

	if !isValidRecordType(recordType) {
		return fmt.Errorf("%w: %s", ErrInvalidRecordType, recordType)
	}

	if strings.TrimSpace(value) == "" {
		return fmt.Errorf("%w: record value cannot be empty", ErrInvalidInput)
	}

	// Determine if PTR record should be deleted as well
	deletePTR := !strings.Contains(record, "*") // Default behavior
	if len(withPtr) > 0 {
		deletePTR = withPtr[0]
	}

	form := neturl.Values{}
	form.Add("action", "delete_record")
	form.Add("record", record)
	form.Add("type", recordType)
	form.Add("value", value) // Specify exact value to delete

	if deletePTR {
		form.Add("ptr", "true")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.baseURL, strings.NewReader(form.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	s.mu.RLock()
	for _, cookie := range s.Cookie {
		req.AddCookie(cookie)
	}
	s.mu.RUnlock()

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete record: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return fmt.Errorf("failed to delete record: %w", err)
	}

	logger.Infof("Successfully deleted DNS record %s (%s) with value %s%s", record, recordType, value,
		func() string {
			if deletePTR {
				return " and PTR record"
			}
			return ""
		}())

	return nil
}

// ReplaceRecord replaces a specific DNS record, optionally specifying the old value to replace
func (s *DNSServer) ReplaceRecord(ctx context.Context, record, oldValue, newValue, recordType string, ttl ...int) error {
	if err := validateRecordName(record); err != nil {
		return err
	}

	if !isValidRecordType(recordType) {
		return fmt.Errorf("%w: %s", ErrInvalidRecordType, recordType)
	}

	if strings.TrimSpace(newValue) == "" {
		return fmt.Errorf("%w: new record value cannot be empty", ErrInvalidInput)
	}

	// Use provided TTL or default to reasonable value
	ttlValue := defaultTTL
	if len(ttl) > 0 {
		if err := validateTTL(ttl[0]); err != nil {
			return err
		}
		ttlValue = ttl[0]
	}

	form := neturl.Values{}
	form.Add("action", "replace_record")
	form.Add("record", record)
	form.Add("new_value", newValue)
	form.Add("type", recordType)
	form.Add("ttl", strconv.Itoa(ttlValue))

	// Add old value if specified (optional parameter)
	// If not specified, all records with given type will be replaced
	if oldValue != "" {
		form.Add("value", oldValue)
	}

	// Add PTR record if not a wildcard record
	if !strings.Contains(record, "*") {
		form.Add("ptr", "true")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.baseURL, strings.NewReader(form.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	s.mu.RLock()
	for _, cookie := range s.Cookie {
		req.AddCookie(cookie)
	}
	s.mu.RUnlock()

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to set record: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return fmt.Errorf("failed to set record: %w", err)
	}

	if oldValue != "" {
		logger.Infof("Successfully replaced DNS record %s (%s) %s > %s with TTL %d", record, recordType, oldValue, newValue, ttlValue)
	} else {
		logger.Infof("Successfully set DNS record %s (%s) > %s with TTL %d", record, recordType, newValue, ttlValue)
	}
	return nil
}

// SetDNSServer sets the DNS server for the manager
func (m *Manager) SetDNSServer(server *DNSServer) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.dnsServer = server
}

// ListZones gets the list of all zones from the DNS server
func (s *DNSServer) ListZones(ctx context.Context) (map[string]struct{}, error) {
	form := neturl.Values{}
	form.Add("action", "list_zones")

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.baseURL, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	s.mu.RLock()
	for _, cookie := range s.Cookie {
		req.AddCookie(cookie)
	}
	s.mu.RUnlock()

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Warnf("Failed to close response body: %v", closeErr)
		}
	}()

	if err := checkResponse(resp); err != nil {
		return nil, err
	}

	// Parse response as map instead of array
	var zonesResponse map[string]map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&zonesResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal zones: %w", err)
	}

	// Extract zone names from the response
	zoneMap := make(map[string]struct{})
	for zoneName := range zonesResponse {
		zoneMap[zoneName] = struct{}{}
	}

	return zoneMap, nil
}
    
// ListRecords lists all DNS records for a given zone
func (m *Manager) ListRecords(ctx context.Context, zone string) ([]Record, error) {
	if m.dnsServer == nil {
		return nil, fmt.Errorf("DNS server not set")
	}

	records, err := m.dnsServer.ListRecords(ctx, zone)
	if err != nil {
		return nil, fmt.Errorf("failed to list records: %w", err)
	}

	return records, nil
}

// normalizeRecordName remove trailing dots or duplicate zone names
func normalizeRecordName(name, zone string) string {
    if strings.HasSuffix(name, ".") {
        name = strings.TrimSuffix(name, ".")
    }
    //if strings.HasSuffix(name, zone) {
    //    name = strings.TrimSuffix(name, "."+zone)
    //}
    return name
}


func (m *Manager) ExecuteCreate(current, backup Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.ReplaceRecord(context.Background(), backup.Name, "", backup.Value, backup.Type, backup.TTL)
	}
	return nil
}

func (m *Manager) ExecuteUpdate(current, backup Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.ReplaceRecord(context.Background(), backup.Name, current.Value, backup.Value, backup.Type, backup.TTL)
	}
	return nil
}

func (m *Manager) ExecuteDelete(record Record, dryRun bool) error {
	if !dryRun {
		return m.dnsServer.DeleteRecordByValue(context.Background(), record.Name, record.Type, record.Value)
	}
	return nil
}

type Conflict struct {
	Name    string
	Type    string
	File1   string
	File2   string
	Record1 Record
	Record2 Record
}

// Load record updates from a YAML file
func LoadRecordUpdates(filePath string) (map[string]map[string][]Record, map[string]map[string][]Record, *Conflict, error) {

    record2update := make(map[string]map[string][]Record)
    record2delete := make(map[string]map[string][]Record)
	var conflicts *Conflict // nil by default

	type fileRecordKey struct {
		Name string
		Type string
	}
	// Map of cluster->zone->name+type -> record + source file
	seen := make(map[string]map[string]map[fileRecordKey]struct {
		Record Record
		File   string
	})
	
    files, err := filepath.Glob(filepath.Join(filePath, "*.yaml"))
	if err != nil {
		panic(err)
	}

    for _, filePath := range files {
        f, err := os.Open(filePath)
        if err != nil {
            logger.Warnf("failed to open file %s: %v", filePath, err)
            continue
        }
        defer func() {
			if closeErr := f.Close(); closeErr != nil {
				logger.Warnf("Failed to close file: %v", closeErr)
			}
		}()

        var root yaml.Node
		if err := yaml.NewDecoder(f).Decode(&root); err != nil {
			fmt.Printf("Failed to decode YAML file %s: %v\n", filePath, err)
			continue
		}

        var dnsNode *yaml.Node
		for i := 0; i < len(root.Content[0].Content); i += 2 {
			key := root.Content[0].Content[i]
			if key.Value == "dns" {
				dnsNode = root.Content[0].Content[i+1]
				break
			}
		}
		if dnsNode == nil {
			continue
		}

		// Loop over clusters
		for i := 0; i < len(dnsNode.Content); i += 2 {
			clusterName := dnsNode.Content[i].Value
			clusterNode := dnsNode.Content[i+1]

			if _, ok := record2update[clusterName]; !ok {
				record2update[clusterName] = make(map[string][]Record)
			}
			if _, ok := record2delete[clusterName]; !ok {
				record2delete[clusterName] = make(map[string][]Record)
			}
			if _, ok := seen[clusterName]; !ok {
				seen[clusterName] = make(map[string]map[fileRecordKey]struct {
					Record Record
					File   string
				})
			}


			// Loop over zones
			for j := 0; j < len(clusterNode.Content); j += 2 {
				zoneName := clusterNode.Content[j].Value
				recordsList := clusterNode.Content[j+1]
				if _, ok := seen[clusterName][zoneName]; !ok {
					seen[clusterName][zoneName] = make(map[fileRecordKey]struct {
						Record Record
						File   string
					})
				}


				for _, item := range recordsList.Content {
					// Check if state == "absent"
					var meta map[string]interface{}
					if err := item.Decode(&meta); err == nil {
						if s, ok := meta["state"]; ok && s == "absent" {
							var rec Record
							if err := item.Decode(&rec); err == nil {
								record2delete[clusterName][zoneName] = append(record2delete[clusterName][zoneName], rec)
							}
							continue // skip adding to record2update
						}
					}

					var rec Record
					if err := item.Decode(&rec); err != nil {
						fmt.Printf("Failed to decode record in file %s: %v\n", filePath, err)
						continue
					}
					key := fileRecordKey{Name: rec.Name, Type: rec.Type}
					if existing, found := seen[clusterName][zoneName][key]; found {
						conf := &Conflict{
							Name:    rec.Name,
							Type:    rec.Type,
							File1:   existing.File,
							File2:   filePath,
							Record1: existing.Record,
							Record2: rec,
						}
						return nil, nil, conf, nil
					} else {
						seen[clusterName][zoneName][key] = struct {
							Record Record
							File   string
						}{Record: rec, File: filePath}
					}
					record2update[clusterName][zoneName] = append(record2update[clusterName][zoneName], rec)
				}
			}
		}
	}
	return record2update, record2delete, conflicts, nil
}

// UpdateRecords handles updating multiple records across zones and clusters
// It processes both updates and deletes from the provided configuration
func (m *Manager) UpdateRecords(cluster string, updates, deletes map[string]map[string][]Record, dryRun bool) error {
	if m.dnsServer == nil {
		return fmt.Errorf("DNS server not configured")
	}

	logger.Infof("Processing updates for cluster: %s", cluster)

	// Get all zones to verify zone existence
	zoneMap, err:= m.dnsServer.ListZones(context.Background())
	if err != nil {
		return fmt.Errorf("failed to list zones: %w", err)
	}

	// Store overall stats
	stats := &RestoreStats{}
	var allChanges []string
	var allWarnings []string
	
	// Process updates by cluster
	for updateCluster, zones := range updates {
		// Skip if a specific cluster was requested and this isn't it
		if cluster != "" && updateCluster != cluster {
			continue
		}

		// Process each zone
		for updateZone, records := range zones {
			// Verify zone exists
			if _, exists := zoneMap[updateZone]; !exists {
				logger.Warnf("Zone '%s' does not exist in cluster '%s', skipping", updateZone, updateCluster)
				continue
			}

			// Get current records from the server
			currentRecords, err := m.ListRecords(context.Background(), updateZone)
			if err != nil {
				return fmt.Errorf("failed to list current records for zone %s: %w", updateZone, err)
			}

			// Skip if no records to update
			if len(records) == 0 {
				logger.Infof("No update records specified for zone '%s', skipping", updateZone)
				continue
			}

			// Convert slice to map for easier processing
			currentMap := make(map[string]Record)
			
			for _, record := range currentRecords {
				normalizedName := normalizeRecordName(record.Name, updateZone)
				key := fmt.Sprintf("%s:%s", normalizedName, record.Type)
				currentMap[key] = record
			}

			// Validate and filter update records
			validUpdateMap := make(map[string]Record)
			var invalidRecords []string

			for _, record := range records {
				// Use the comprehensive validateRecord function instead of multiple separate validations
				if err := validateRecord(record); err != nil {
					invalidRecords = append(invalidRecords, fmt.Sprintf("%s (%s): %v", record.Name, record.Type, err))
					continue
				}
				
				// Record is valid, add to map
				normalizedName := normalizeRecordName(record.Name, updateZone)
				key := fmt.Sprintf("%s:%s", normalizedName, record.Type)
				validUpdateMap[key] = record
			}

			// Report invalid records
			if len(invalidRecords) > 0 {
				logger.Errorf("Skipping %d invalid records in zone '%s':", len(invalidRecords), updateZone)
				for _, msg := range invalidRecords {
					logger.Errorf("  - %s", msg)
				}
			}

			// If no valid records after filtering, skip this zone
			if len(validUpdateMap) == 0 {
				logger.Warnf("No valid update records for zone '%s' after validation, skipping", updateZone)
				continue
			}

			// Process records from updates (create/update)
			var zoneChanges []string
			var zoneWarnings []string
			
						if err != nil {
				return fmt.Errorf("failed to get cluster configuration: %w", err)
			}
			
			// Process records (create/update)
			if err := m.processBackupRecords(validUpdateMap, currentMap, stats, &zoneChanges, dryRun); err != nil {
				return fmt.Errorf("failed to update records for zone %s: %w", updateZone, err)
			}

						// Add zone identifier to changes and warnings
			for i, change := range zoneChanges {
				zoneChanges[i] = fmt.Sprintf("[%s] %s", updateZone, change)
			}
			for i, warning := range zoneWarnings {
				zoneWarnings[i] = fmt.Sprintf("[%s] %s", updateZone, warning)
			}
			
			allChanges = append(allChanges, zoneChanges...)
			allWarnings = append(allWarnings, zoneWarnings...)
		}
	}

	// Process deletes by cluster
	for deleteCluster, zones := range deletes {
		// Skip if a specific cluster was requested and this isn't it
		if cluster != "" && deleteCluster != cluster {
			continue
		}

		// Process each zone
		for deleteZone, records := range zones {
			// Verify zone exists
			if _, exists := zoneMap[deleteZone]; !exists {
				logger.Warnf("Zone '%s' does not exist in cluster '%s', skipping", deleteZone, deleteCluster)
				continue
			}

			// Skip if no records to delete
			if len(records) == 0 {
				logger.Infof("No delete records specified for zone '%s', skipping", deleteZone)
				continue
			}

			logger.Infof("Processing %d record deletions for zone '%s' in cluster '%s'", len(records), deleteZone, deleteCluster)
			
			// Get current records from the server to verify deletion targets exist
			currentRecords, err := m.ListRecords(context.Background(), deleteZone)
			if err != nil {
				return fmt.Errorf("failed to list current records for zone %s: %w", deleteZone, err)
			}
			
			// Build maps for efficient record existence checking
			recordValueMap := make(map[string]map[string][]string) // type -> name -> []values
			for _, record := range currentRecords {
				normalizedName := normalizeRecordName(record.Name, deleteZone)
				
				if _, exists := recordValueMap[record.Type]; !exists {
					recordValueMap[record.Type] = make(map[string][]string)
				}
				
				recordValueMap[record.Type][normalizedName] = append(
					recordValueMap[record.Type][normalizedName], record.Value)
			}

			// Process each record to delete
			deletionCount := 0
			skippedCount := 0
			
			for _, record := range records {
				// Use the comprehensive validateRecord function instead of multiple separate validations
				if err := validateRecord(record); err != nil {
					logger.Warnf("Skipping invalid record %s (%s): %v", record.Name, record.Type, err)
					skippedCount++
					continue
				}
				
				// Check if record actually exists before attempting deletion
				normalizedName := normalizeRecordName(record.Name, deleteZone)
				recordExists := false
				
				if typeMap, typeExists := recordValueMap[record.Type]; typeExists {
					if values, nameExists := typeMap[normalizedName]; nameExists {
						for _, value := range values {
							if value == record.Value {
								recordExists = true
								break
							}
						}
					}
				}
				
				if !recordExists {
					logger.Warnf("Record %s (%s) with value %s doesn't exist, skipping deletion", 
						record.Name, record.Type, record.Value)
					skippedCount++
					continue
				}
				
				// Handle deletion directly
				if dryRun {
					logger.Infof("[DRY RUN] Would delete record %s (%s) with value %s", 
						record.Name, record.Type, record.Value)
				} else {
					logger.Infof("Deleting record %s (%s) with value %s", 
						record.Name, record.Type, record.Value)
					
					// All validation passed, proceed with deletion
					if err := m.dnsServer.DeleteRecordByValue(context.Background(), 
						record.Name, record.Type, record.Value); err != nil {
						logger.Warnf("Failed to delete record %s (%s): %v", 
							record.Name, record.Type, err)
						skippedCount++
						continue
					}
				}
				
				change := fmt.Sprintf("[%s] - DELETE: %s (%s) %s [TTL: %d]", 
					deleteZone, record.Name, record.Type, record.Value, record.TTL)
				allChanges = append(allChanges, change)
				stats.Deleted++
				deletionCount++
			}
			
			// Report summary for this zone
			if skippedCount > 0 {
				logger.Warnf("Skipped %d invalid or non-existent records in zone '%s'", 
					skippedCount, deleteZone)
			}
			
			logger.Infof("Processed %d deletions for zone '%s' (%d successful, %d skipped)", 
				len(records), deleteZone, deletionCount, skippedCount)
		}
	}
	
	// Report overall results
	date := time.Now().Format("2006-01-02")
	m.reportUpdateResults(cluster, date, stats, allChanges, allWarnings, dryRun)
	
	return nil
}

// reportUpdateResults logs the results of the update operation
func (m *Manager) reportUpdateResults(cluster, date string, stats *RestoreStats, changes, warnings []string, dryRun bool) {
	operationType := "Update"
	if dryRun {
		operationType = "DRY RUN Update"
	}
	
	logger.Infof("%s operation completed for cluster %s on %s:", operationType, cluster, date)
	logger.Infof("  Total records processed: %d", stats.Processed)
	logger.Infof("  Records created: %d", stats.Created)
	logger.Infof("  Records updated: %d", stats.Updated)
	logger.Infof("  Records unchanged (kept): %d", stats.Kept)
	logger.Infof("  Records deleted: %d", stats.Deleted)
	logger.Infof("  Extra records (warnings): %d", stats.Warnings)

	if len(changes) > 0 {
		prefix := "Made"
		if dryRun {
			prefix = "Would make"
		}
		logger.Infof("%s the following changes:", prefix)
		for _, change := range changes {
			logger.Infof("  %s", change)
		}
	}

	if len(warnings) > 0 {
		logger.Infof("Warnings (extra records found):")
		for _, warning := range warnings {
			logger.Infof("  %s", warning)
		}
		logger.Infof("Note: These records were left unchanged. Use state: absent to delete them.")
	}

	if len(changes) == 0 && len(warnings) == 0 {
		logger.Infof("No changes needed - DNS records match desired state perfectly")
	}
}

// validateRecord validates an entire DNS record and returns a descriptive error if invalid
func validateRecord(record Record) error {
	// Validate record name
	if err := validateRecordName(record.Name); err != nil {
		return fmt.Errorf("invalid name (%s): %w", record.Name, err)
	}

	// Validate record type
	if !isValidRecordType(record.Type) {
		return fmt.Errorf("invalid record type: %s", record.Type)
	}

	// Validate record value
	if strings.TrimSpace(record.Value) == "" {
		return fmt.Errorf("empty value not allowed")
	}

	// Validate TTL
	if err := validateTTL(record.TTL); err != nil {
		return fmt.Errorf("invalid TTL (%d): %w", record.TTL, err)
	}

		// Type-specific validation using switch case
	switch record.Type {
	case "A":
		if !isValidIP(record.Value) {
			return fmt.Errorf("invalid IP address (%s) for type %s", record.Value, record.Type)
		}
	case "CNAME":
		if !isValidCNAME(record.Value) {
			return fmt.Errorf("invalid CNAME format: %s", record.Value)
		}
	// TODO: Add more record types as needed
	// case "AAAA":
	//     if !isValidIPv6(record.Value) {
	//         return fmt.Errorf("invalid IPv6 address for AAAA record: %s", record.Value)
	//     }
	// case "MX":
	//     if !isValidMX(record.Value) {
	//         return fmt.Errorf("invalid MX record format: %s", record.Value)
	//     }
	// case "TXT":
	//     if !isValidTXT(record.Value) {
	//         return fmt.Errorf("invalid TXT record format: %s", record.Value)
	//     }
	default:
		// For other record types, we just ensure value is not empty (already checked above)
		// You can add more specific validations as needed
	}

	return nil
}

// validateIP checks if the provided string is a valid IPv4 address
func isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil && strings.Count(ip, ":") == 0 // Ensure it's IPv4
}

// validateCNAME checks if the provided string is a valid CNAME format
func isValidCNAME(cname string) bool {
	// A simple CNAME validation: must be a valid domain name
	// This can be extended with more complex rules if needed
	if strings.TrimSpace(cname) == "" {
		return false
	}
	if strings.Contains(cname, " ") || strings.Contains(cname, "\t") {
		return false
	}
	return true
}

// ValidateRecord is a public wrapper for validateRecord that can be used by other packages
func ValidateRecord(record Record) error {
	return validateRecord(record)
}

// ValidationError represents a validation error with context
type ValidationError struct {
	File    string
	Field   string
	Record  string
	Message string
}

// LoadAndValidateRecordUpdates loads DNS records from YAML files with comprehensive validation
// It checks app_code matching, validates all DNS records, and detects conflicts
func LoadAndValidateRecordUpdates(filePath string, archApps []string) (map[string]map[string][]Record, map[string]map[string][]Record, *Conflict, []ValidationError, error) {
	var validationErrors []ValidationError
	
	// Use the existing LoadRecordUpdates function
	updates, deletes, conflict, err := LoadRecordUpdates(filePath)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to load records: %w", err)
	}
	
	// If there's a conflict, return it immediately
	if conflict != nil {
		return updates, deletes, conflict, validationErrors, nil
	}
	
	// Additional validation for app_code matching filename
	files, err := filepath.Glob(filepath.Join(filePath, "*.yaml"))
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to scan directory: %w", err)
	}
	
	for _, file := range files {
		fileName := filepath.Base(file)
		fileNameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))
		
		// Validate app_code matches filename
		if err := validateAppCodeInFile(file, fileNameWithoutExt); err != nil {
			validationErrors = append(validationErrors, ValidationError{
				File:    fileName,
				Field:   "app_code",
				Message: err.Error(),
			})
		}
		if !checkContains(archApps, fileNameWithoutExt) {
			// If app_code is found in architecture apps, no error
			validationErrors = append(validationErrors, ValidationError{
				File:    fileName,
				Field:   "app_code",
				Message: fmt.Sprintf("app_code '%s' not found in architecture apps", fileNameWithoutExt),
			})
		}
	}
	
	// Validate all DNS records
	recordErrors := validateAllDNSRecords(updates, deletes)
	validationErrors = append(validationErrors, recordErrors...)
	
	return updates, deletes, nil, validationErrors, nil
}

// validateAppCodeInFile checks if app_code in file matches the filename
func validateAppCodeInFile(filePath, expectedAppCode string) error {
	f, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			logger.Warnf("Failed to close file: %v", closeErr)
		}
	}()
	
	var root yaml.Node
	if err := yaml.NewDecoder(f).Decode(&root); err != nil {
		return fmt.Errorf("failed to parse YAML: %w", err)
	}
	
	if len(root.Content) == 0 || len(root.Content[0].Content) == 0 {
		return fmt.Errorf("empty or invalid YAML structure")
	}
	
	// Find app_code in the YAML
	var appCode string
	for i := 0; i < len(root.Content[0].Content); i += 2 {
		key := root.Content[0].Content[i]
		if key.Value == "app_code" {
			if i+1 < len(root.Content[0].Content) {
				appCode = root.Content[0].Content[i+1].Value
				break
			}
		}
	}
	
	if appCode == "" {
		return fmt.Errorf("missing app_code field")
	}
	
	if appCode != expectedAppCode {
		return fmt.Errorf("app_code '%s' doesn't match filename '%s'", appCode, expectedAppCode)
	}

	return nil
}

// validateAllDNSRecords validates all DNS records in the loaded data
func validateAllDNSRecords(updates, deletes map[string]map[string][]Record) []ValidationError {
	var errors []ValidationError
	
	// Validate update records
	for cluster, zones := range updates {
		for zone, records := range zones {
			for i, record := range records {
				if err := ValidateRecord(record); err != nil {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("cluster[%s].zone[%s].record[%d]", cluster, zone, i),
						Record:  fmt.Sprintf("%s (%s)", record.Name, record.Type),
						Message: err.Error(),
					})
				}
			}
		}
	}
	
	// Validate delete records
	for cluster, zones := range deletes {
		for zone, records := range zones {
			for i, record := range records {
				if err := ValidateRecord(record); err != nil {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("cluster[%s].zone[%s].delete_record[%d]", cluster, zone, i),
						Record:  fmt.Sprintf("%s (%s)", record.Name, record.Type),
						Message: err.Error(),
					})
				}
				
				// Additional validation for delete records - ensure required fields
				if record.Name == "" {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("cluster[%s].zone[%s].delete_record[%d]", cluster, zone, i),
						Record:  fmt.Sprintf("record[%d]", i),
						Message: "delete record missing 'name' field",
					})
				}
				if record.Type == "" {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("cluster[%s].zone[%s].delete_record[%d]", cluster, zone, i),
						Record:  fmt.Sprintf("record[%d]", i),
						Message: "delete record missing 'type' field",
					})
				}
				if record.Value == "" {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("cluster[%s].zone[%s].delete_record[%d]", cluster, zone, i),
						Record:  fmt.Sprintf("record[%d]", i),
						Message: "delete record missing 'value' field",
					})
				}
			}
		}
	}
	
	return errors
}

// ExtractAppCodes extracts app codes from the architecture apps file
func ExtractAppCodes(path string) (appCodes []string, err error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read architecture apps file: %w", err)
	}
	var apps ArchAppConfig

	err = yaml.Unmarshal(data, &apps)
	if err != nil {
		return nil, err
	}
	for _, app := range apps.Apps {
		appCode := strings.ToLower(app.AppCode)
		appCodes = append(appCodes, appCode)
	}
	return appCodes, nil
}

func checkContains(slice []string, element string) bool {
	for _, item := range slice {
		if item == element {
			return true
		}
	}
	return false
}