// Package logger provides structured logging for the pulse application.
package logger

import (
	"fmt"
	"log"
	"os"
)

// Level represents logging levels
type Level int

const (
	LevelDebug Level = iota
	LevelInfo
	LevelWarn
	LevelError
)

var levelStrings = [4]string{"DEBUG", "INFO", "WARN", "ERROR"}

// Logger represents a structured logger
type Logger struct {
	level  Level
	logger *log.Logger
}

var defaultLogger *Logger

func init() {
	defaultLogger = New(LevelInfo)
}

// New creates a new logger with the specified level
func New(level Level) *Logger {
	return &Logger{
		level:  level,
		logger: log.New(os.Stdout, "", log.LstdFlags),
	}
}

// SetLevel sets the logging level for the default logger
func SetLevel(level Level) {
	defaultLogger.level = level
}

// logf is an optimized internal method that reduces string allocations
func (l *Logger) logf(level Level, format string, args ...interface{}) {
	if l.level <= level {
		var message string
		if len(args) == 0 {
			message = format
		} else {
			message = fmt.Sprintf(format, args...)
		}
		l.logger.Printf("[%s] %s", levelStrings[level], message)
	}
}

// Debug logs a debug message
func Debug(msg string) {
	defaultLogger.logf(LevelDebug, msg)
}

// Debugf logs a formatted debug message
func Debugf(format string, args ...interface{}) {
	defaultLogger.logf(LevelDebug, format, args...)
}

// Info logs an info message
func Info(msg string) {
	defaultLogger.logf(LevelInfo, msg)
}

// Infof logs a formatted info message
func Infof(format string, args ...interface{}) {
	defaultLogger.logf(LevelInfo, format, args...)
}

// Warn logs a warning message
func Warn(msg string) {
	defaultLogger.logf(LevelWarn, msg)
}

// Warnf logs a formatted warning message
func Warnf(format string, args ...interface{}) {
	defaultLogger.logf(LevelWarn, format, args...)
}

// Error logs an error message
func Error(msg string) {
	defaultLogger.logf(LevelError, msg)
}

// Errorf logs a formatted error message
func Errorf(format string, args ...interface{}) {
	defaultLogger.logf(LevelError, format, args...)
}

// Debug logs a debug message
func (l *Logger) Debug(msg string) {
	l.logf(LevelDebug, msg)
}

// Debugf logs a formatted debug message
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.logf(LevelDebug, format, args...)
}

// Info logs an info message
func (l *Logger) Info(msg string) {
	l.logf(LevelInfo, msg)
}

// Infof logs a formatted info message
func (l *Logger) Infof(format string, args ...interface{}) {
	l.logf(LevelInfo, format, args...)
}

// Warn logs a warning message
func (l *Logger) Warn(msg string) {
	l.logf(LevelWarn, msg)
}

// Warnf logs a formatted warning message
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.logf(LevelWarn, format, args...)
}

// Error logs an error message
func (l *Logger) Error(msg string) {
	l.logf(LevelError, msg)
}

// Errorf logs a formatted error message
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.logf(LevelError, format, args...)
}
