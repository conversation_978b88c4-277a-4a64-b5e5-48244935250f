package logger

import (
	"bytes"
	"log"
	"strings"
	"testing"
)

func TestLoggerLevels(t *testing.T) {
	var buf bytes.Buffer
	logger := New(LevelWarn)
	logger.logger = log.New(&buf, "", 0)

	// Debug and Info should not be logged at Warn level
	logger.Debug("debug message")
	logger.Info("info message")

	// Warn and <PERSON>rror should be logged
	logger.Warn("warn message")
	logger.Error("error message")

	output := buf.String()

	if strings.Contains(output, "debug message") {
		t.<PERSON>rror("Debug message should not be logged at Warn level")
	}

	if strings.Contains(output, "info message") {
		t.<PERSON>rror("Info message should not be logged at Warn level")
	}

	if !strings.Contains(output, "[WARN] warn message") {
		t.Error("Warn message should be logged at Warn level")
	}

	if !strings.Contains(output, "[ERROR] error message") {
		t.<PERSON><PERSON>("Error message should be logged at Warn level")
	}
}

func TestLoggerFormatting(t *testing.T) {
	var buf bytes.Buffer
	logger := New(LevelInfo)
	logger.logger = log.New(&buf, "", 0)

	logger.Infof("formatted message: %d", 42)

	output := buf.String()
	expected := "[INFO] formatted message: 42"

	if !strings.Contains(output, expected) {
		t.Errorf("Expected output to contain %q, got %q", expected, output)
	}
}

func TestDefaultLogger(t *testing.T) {
	// Test that default logger functions work without panicking
	Debug("test debug")
	Info("test info")
	Warn("test warn")
	Error("test error")

	Debugf("test debug %d", 1)
	Infof("test info %d", 2)
	Warnf("test warn %d", 3)
	Errorf("test error %d", 4)
}

func TestSetLevel(t *testing.T) {
	originalLevel := defaultLogger.level
	defer func() {
		defaultLogger.level = originalLevel
	}()

	SetLevel(LevelError)
	if defaultLogger.level != LevelError {
		t.Errorf("Expected level to be %d, got %d", LevelError, defaultLogger.level)
	}
}
