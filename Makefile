# Makefile for pulse

.PHONY: build clean test lint fmt vet install run help docker-build docker-run

# Variables
BINARY_NAME=pulse
BUILD_DIR=bin
MAIN_PACKAGE=.
VERSION?=$(shell git describe --tags --always --dirty)
GIT_COMMIT=$(shell git rev-parse HEAD)
BUILD_DATE=$(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
LDFLAGS=-X git.homecredit.net/country/vn/platform/pulse/internal/version.Version=$(VERSION) \
        -X git.homecredit.net/country/vn/platform/pulse/internal/version.GitCommit=$(GIT_COMMIT) \
        -X git.homecredit.net/country/vn/platform/pulse/internal/version.BuildDate=$(BUILD_DATE)

# Docker variables
DOCKER_IMAGE=$(BINARY_NAME)
DOCKER_TAG?=latest

# Default target
all: lint test build

# Build the binary
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build -ldflags "$(LDFLAGS)" -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PACKAGE)

# Clean build artifacts
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)
	@go clean

# Run tests
test:
	@echo "Running tests..."
	@go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -race -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html

# Run linter
lint:
	@echo "Running linter..."
	@golangci-lint run

# Format code
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Run go vet
vet:
	@echo "Running go vet..."
	@go vet ./...

# Install dependencies
install:
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy

# Run the application
run:
	@go run .

# Install binary to GOPATH/bin
install-binary:
	@echo "Installing binary..."
	@go install .

# Generate documentation
docs:
	@echo "Generating documentation..."
	@go doc -all . > docs.txt

# Check for security vulnerabilities
security:
	@echo "Checking for security vulnerabilities..."
	@go list -json -m all | nancy sleuth

# Update dependencies
update:
	@echo "Updating dependencies..."
	@go get -u ./...
	@go mod tidy

# Help
help:
	@echo "Available targets:"
	@echo "  build           - Build the binary"
	@echo "  clean           - Clean build artifacts"
	@echo "  test            - Run tests"
	@echo "  test-coverage   - Run tests with coverage"
	@echo "  lint            - Run linter"
	@echo "  fmt             - Format code"
	@echo "  vet             - Run go vet"
	@echo "  install         - Install dependencies"
	@echo "  run             - Run the application"
	@echo "  install-binary  - Install binary to GOPATH/bin"
	@echo "  docs            - Generate documentation"
	@echo "  security        - Check for security vulnerabilities"
	@echo "  update          - Update dependencies"
	@echo "  docker-build    - Build Docker image"
	@echo "  docker-run      - Run application in Docker container"
	@echo "  help            - Show this help"

# Docker targets
docker-build:
	@echo "Building Docker image..."
	@docker build --build-arg VERSION=$(VERSION) \
		--build-arg GIT_COMMIT=$(GIT_COMMIT) \
		--build-arg BUILD_DATE=$(BUILD_DATE) \
		-t $(DOCKER_IMAGE):$(DOCKER_TAG) .

docker-run:
	@echo "Running Docker container..."
	@docker run --rm -it $(DOCKER_IMAGE):$(DOCKER_TAG) --help
