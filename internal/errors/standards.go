// Package errors defines error standards and validation patterns for the pulse application.
// This ensures consistent error messaging and handling across all modules.
package errors

import (
	"fmt"
	"strings"
)

// Error message format standards:
// 1. Use error wrapping with fmt.Errorf("%w: additional context", originalError)
// 2. Start error messages with lowercase unless they're proper nouns
// 3. Use consistent prefixes for operation types
// 4. Include relevant context (file paths, record names, etc.)
// 5. Use predefined error types for common validation failures

// Common error prefixes for consistent messaging
const (
	PrefixValidation = "validation failed"
	PrefixConnection = "connection failed"
	PrefixOperation  = "operation failed"
	PrefixParsing    = "parsing failed"
	PrefixNotFound   = "not found"
	PrefixConfig     = "configuration error"
)

// ValidationError creates a standardized validation error
func ValidationError(field, reason string) error {
	return fmt.Errorf("%s: %s %s", PrefixValidation, field, reason)
}

// OperationError creates a standardized operation error with context
func OperationError(operation, context string, err error) error {
	if err == nil {
		return fmt.Errorf("%s: %s", PrefixOperation, operation)
	}
	return fmt.Errorf("%s: %s in %s: %w", PrefixOperation, operation, context, err)
}

// NotFoundError creates a standardized not found error
func NotFoundError(resource, identifier string) error {
	return fmt.Errorf("%s: %s '%s'", PrefixNotFound, resource, identifier)
}

// ConfigError creates a standardized configuration error
func ConfigError(setting, reason string) error {
	return fmt.Errorf("%s: %s - %s", PrefixConfig, setting, reason)
}

// ValidateStringNotEmpty validates that a string field is not empty
func ValidateStringNotEmpty(fieldName, value string) error {
	if strings.TrimSpace(value) == "" {
		return ValidationError(fieldName, "cannot be empty")
	}
	return nil
}

// ValidateStringLength validates string length constraints
func ValidateStringLength(fieldName, value string, min, max int) error {
	if err := ValidateStringNotEmpty(fieldName, value); err != nil {
		return err
	}

	length := len(strings.TrimSpace(value))
	if length < min {
		return ValidationError(fieldName, fmt.Sprintf("too short (minimum %d characters)", min))
	}
	if length > max {
		return ValidationError(fieldName, fmt.Sprintf("too long (maximum %d characters)", max))
	}
	return nil
}

// ValidateSliceNotEmpty validates that a slice is not empty
func ValidateSliceNotEmpty(fieldName string, slice []string) error {
	if len(slice) == 0 {
		return ValidationError(fieldName, "cannot be empty")
	}
	return nil
}

// ValidateIntRange validates that an integer is within a specified range
func ValidateIntRange(fieldName string, value, min, max int) error {
	if value < min {
		return ValidationError(fieldName, fmt.Sprintf("too low (minimum %d)", min))
	}
	if value > max {
		return ValidationError(fieldName, fmt.Sprintf("too high (maximum %d)", max))
	}
	return nil
}

// WrapError wraps an error with additional context following standard format
func WrapError(err error, operation, context string) error {
	if err == nil {
		return nil
	}

	if context != "" {
		return fmt.Errorf("%s %s: %w", operation, context, err)
	}
	return fmt.Errorf("%s: %w", operation, err)
}

// ChainErrors combines multiple errors into a single error message
func ChainErrors(operation string, errors []error) error {
	if len(errors) == 0 {
		return nil
	}

	if len(errors) == 1 {
		return WrapError(errors[0], operation, "")
	}

	var messages []string
	for _, err := range errors {
		if err != nil {
			messages = append(messages, err.Error())
		}
	}

	return fmt.Errorf("%s: multiple errors occurred: %s", operation, strings.Join(messages, "; "))
}
