package version

import (
	"fmt"
	"runtime"
)

// Build information that can be set at compile time using ldflags
var (
	Version   = "dev"
	GitCommit = "unknown"
	BuildDate = "unknown"
	GoVersion = runtime.Version()
)

// GetVersionInfo returns detailed version information
func GetVersionInfo() string {
	return fmt.Sprintf("pulse %s (commit: %s, built: %s, go: %s)",
		Version, GitCommit, BuildDate, GoVersion)
}

// Info returns detailed version information (deprecated, use GetVersionInfo)
func Info() string {
	return GetVersionInfo()
}

// Short returns short version string
func Short() string {
	return Version
}
