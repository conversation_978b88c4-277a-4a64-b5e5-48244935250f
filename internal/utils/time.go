package utils

import (
	"time"
)

// VietnamLocation represents Asia/Ho_Chi_Minh timezone (GMT+7)
var VietnamLocation *time.Location

func init() {
	var err error
	VietnamLocation, err = time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		// Fallback to fixed offset if timezone loading fails
		VietnamLocation = time.FixedZone("GMT+7", 7*60*60)
	}
}

// NowInVietnam returns the current time in Vietnam timezone (Asia/Ho_Chi_Minh)
func NowInVietnam() time.Time {
	return time.Now().In(VietnamLocation)
}

// FormatVietnamTime formats a time in Vietnam timezone with the given layout
func FormatVietnamTime(t time.Time, layout string) string {
	return t.In(VietnamLocation).Format(layout)
}

// GetVietnamTimeFromUTC converts UTC time to Vietnam timezone
func GetVietnamTimeFromUTC(utc time.Time) time.Time {
	return utc.In(VietnamLocation)
}
