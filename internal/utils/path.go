// Package utils provides common utility functions for the pulse application.
package utils

import (
	"os"
	"path/filepath"
	"strings"
)

// ExpandPath expands ~ to user home directory in file paths.
// It handles paths starting with ~/ by replacing them with the user's home directory.
func ExpandPath(path string) string {
	if strings.HasPrefix(path, "~/") {
		if homeDir, err := os.UserHomeDir(); err == nil {
			return filepath.Join(homeDir, path[2:])
		}
	}
	return path
}
