package utils

import (
	"strings"
	"testing"
)

func TestExpandPath(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		checkFn  func(string, string) bool
	}{
		{
			name:     "home directory expansion",
			input:    "~/test/path",
			expected: "test/path",
			checkFn: func(result, expected string) bool {
				return !strings.HasPrefix(result, "~") && strings.HasSuffix(result, expected)
			},
		},
		{
			name:     "absolute path",
			input:    "/absolute/path",
			expected: "/absolute/path",
			checkFn: func(result, expected string) bool {
				return result == expected
			},
		},
		{
			name:     "relative path",
			input:    "relative/path",
			expected: "relative/path",
			checkFn: func(result, expected string) bool {
				return result == expected
			},
		},
		{
			name:     "empty path",
			input:    "",
			expected: "",
			checkFn: func(result, expected string) bool {
				return result == expected
			},
		},
		{
			name:     "just tilde",
			input:    "~",
			expected: "~",
			checkFn: func(result, expected string) bool {
				return result == expected
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ExpandPath(tt.input)
			if !tt.checkFn(result, tt.expected) {
				t.Errorf("ExpandPath(%q) = %q, validation failed for expected %q", tt.input, result, tt.expected)
			}
		})
	}
}
