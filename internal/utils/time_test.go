package utils

import (
	"testing"
	"time"
)

func TestVietnamTimezone(t *testing.T) {
	// Test that VietnamLocation is properly initialized
	if VietnamLocation == nil {
		t.<PERSON>al("VietnamLocation should not be nil")
	}

	// Test timezone name
	name, _ := time.Now().In(VietnamLocation).Zone()
	if name != "+07" && name != "ICT" {
		t.Logf("Timezone name: %s (expected +07 or ICT)", name)
	}

	// Test timezone offset (should be +7 hours = 25200 seconds)
	_, offset := time.Now().In(VietnamLocation).Zone()
	expectedOffset := 7 * 60 * 60 // 7 hours in seconds
	if offset != expectedOffset {
		t.<PERSON>rf("Expected offset %d seconds (+7 hours), got %d seconds", expectedOffset, offset)
	}
}

func TestNowInVietnam(t *testing.T) {
	vietnamTime := NowInVietnam()
	utcTime := time.Now().UTC()

	// Vietnam time should be 7 hours ahead of UTC
	expectedVietnamTime := utcTime.In(VietnamLocation)

	// Allow for small differences due to execution time
	timeDiff := vietnamTime.Sub(expectedVietnamTime)
	if timeDiff < 0 {
		timeDiff = -timeDiff
	}

	if timeDiff > time.Second {
		t.Errorf("Vietnam time differs too much from expected: %v", timeDiff)
	}

	// Test that timezone is correctly set
	_, offset := vietnamTime.Zone()
	expectedOffset := 7 * 60 * 60 // 7 hours
	if offset != expectedOffset {
		t.Errorf("Expected timezone offset +7 hours (%d seconds), got %d seconds", expectedOffset, offset)
	}
}

func TestFormatVietnamTime(t *testing.T) {
	// Create a test time in UTC
	testTime := time.Date(2025, 1, 15, 12, 30, 45, 0, time.UTC)

	// Format in Vietnam timezone
	formatted := FormatVietnamTime(testTime, "2006-01-02 15:04:05")

	// The time should be converted to Vietnam timezone (+7 hours)
	expected := "2025-01-15 19:30:45" // 12:30 UTC + 7 hours = 19:30 Vietnam time
	if formatted != expected {
		t.Errorf("Expected %s, got %s", expected, formatted)
	}
}

func TestGetVietnamTimeFromUTC(t *testing.T) {
	// Create a test time in UTC
	utcTime := time.Date(2025, 1, 15, 12, 30, 45, 0, time.UTC)

	// Convert to Vietnam timezone
	vietnamTime := GetVietnamTimeFromUTC(utcTime)

	// Should be 7 hours ahead
	expected := time.Date(2025, 1, 15, 19, 30, 45, 0, VietnamLocation)
	if !vietnamTime.Equal(expected) {
		t.Errorf("Expected %v, got %v", expected, vietnamTime)
	}

	// Test timezone
	_, offset := vietnamTime.Zone()
	expectedOffset := 7 * 60 * 60
	if offset != expectedOffset {
		t.Errorf("Expected timezone offset +7 hours, got %d seconds", offset)
	}
}

func TestTimezoneConsistency(t *testing.T) {
	// Test that all our time functions return consistent timezone
	now := NowInVietnam()
	utcNow := time.Now().UTC()
	convertedTime := GetVietnamTimeFromUTC(utcNow)

	// All should have the same timezone offset
	_, nowOffset := now.Zone()
	_, convertedOffset := convertedTime.Zone()

	if nowOffset != convertedOffset {
		t.Errorf("Timezone offsets are inconsistent: %d vs %d", nowOffset, convertedOffset)
	}

	// Both should be +7 hours
	expectedOffset := 7 * 60 * 60
	if nowOffset != expectedOffset {
		t.Errorf("Expected +7 hours offset (%d seconds), got %d seconds", expectedOffset, nowOffset)
	}
}
