variables:
  REGISTRY: "registry.vn.eit.zone"
  PULSE_IMAGE_NAME: "external_platform/pulse"
  RELEASE_IMAGE: "$REGISTRY/$PULSE_IMAGE_NAME:$CI_COMMIT_TAG"
  BUILD_ARGS: "--build-arg http_proxy=${http_proxy} --build-arg https_proxy=${https_proxy} --build-arg no_proxy=${no_proxy}"

default:
  image: registry.vn.eit.zone/external/docker:24.0.5
  services:
    - name: registry.vn.eit.zone/external/docker:24.0.5-dind
      alias: docker
  tags:
    - vn-small

stages:
  - release

# Release template for custom registry
.release_template: &release_template
  stage: release
  before_script:
    - docker login -u ${REGISTRY_USER} -p ${REGISTRY_PASSWORD} ${REGISTRY}
    - export DOCKER_BUILDKIT=1
  script:
    - BUILD_DATE=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    - docker build ${BUILD_ARGS} --build-arg VERSION=$CI_COMMIT_TAG --build-arg GIT_COMMIT=$CI_COMMIT_SHA --build-arg BUILD_DATE=$BUILD_DATE -t "$RELEASE_IMAGE" .
    - docker push "$RELEASE_IMAGE"
  after_script:
    - docker logout ${REGISTRY}
  only:
    - tags

# Release job using the template
release:
  <<: *release_template 