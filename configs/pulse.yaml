# Pulse Configuration Example
# Place this file as pulse.yaml in your working directory

# Global settings
global:
  git:
    base_url: "git.homecredit.net"
    base_path: "country/vn/k8s/deploy/backup"
  
  # Global credentials configuration for Vault integration
  creds:
    url: "https://vault.tools-country-k8s-pdc-green.hcnet.vn/"
    dns_path: "apps/platform-pdc/data/onboarding/dns-credentials"

# DNS cluster configurations
dns:
  int:
    url: "https://run-tools.homecredit.vn/dnsphpadmin/api.php"
    backup_dir: "backups/int"
    git_project: "dns-backup"
    git_tmp_dir: "git-repos/dns-backup"
    retention_days: 30
    push_to_git: true
    api_token_data_key: "int_token"

  # External environment  
  ext:
    url: "https://run-tools.homecredit.vn/dnsphpadmin_ext/api.php"
    backup_dir: "backups/ext"
    git_project: "dns-backup"
    git_tmp_dir: "git-repos/dns-backup"
    retention_days: 14
    push_to_git: true
    api_token_data_key: "ext_token"


# Usage Examples:
# 
# Environment Variables:
#   # Vault configuration (VAULT_TOOLS_ADDR has default from config)
#   export VAULT_TOOLS_ADDR="https://vault.tools-country-k8s-pdc-green.hcnet.vn/"  # Optional - uses config default
#   export VAULT_TOOLS_TOKEN="your-vault-token"  # Required for Vault integration
#   
#   # GitLab API token - for git operations (only needed when push_to_git: true)
#   export GITLAB_TOKEN="your-gitlab-api-token"
#   
#   # Optional - can also use command flags
#   export DNS_CLUSTER="prod"
#   export DNS_ZONES="example.com,test.com"
#
# Commands:
#   # Basic backup (uses Vault for DNS token)
#   pulse dns backup --cluster int --zones example.com
#   
#   # With GitLab token for git operations (when push_to_git: true)
#   pulse dns backup --cluster int --zones example.com --gitlab-token your-gitlab-token
#   
#   # Restore
#   pulse dns restore --cluster int --zones example.com --date 2024-01-01
#   
#   # Show version
#   pulse version
#
# Vault Secret Structure:
#   # At path: secret/data/dns/tokens
#   {
#     "data": {
#       "prod_token": "dns-api-token-for-prod",
#       "staging_token": "dns-api-token-for-staging"
#     }
#   } 