# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Pulse is a DNS management and backup tool built in Go with Git integration and HashiCorp Vault support. It provides backup, restore, and update operations for DNS records across multiple clusters with version control capabilities.

## Development Commands

### Build & Run
```bash
# Build the binary
make build

# Build with custom version/commit info  
make build VERSION=v1.0.0

# Run the application directly
make run

# Install dependencies
make install
```

### Testing
```bash
# Run all tests
make test

# Run tests with coverage report  
make test-coverage
# Opens coverage.html with detailed coverage report
```

### Code Quality
```bash
# Run linter (requires golangci-lint)
make lint

# Format code
make fmt

# Run go vet
make vet

# Check for security vulnerabilities
make security
```

### Docker
```bash
# Build Docker image
make docker-build

# Run in Docker container
make docker-run
```

## Code Architecture

### Core Components

**DNS Manager (`pkg/dns/manager.go`)**
- Central component managing DNS operations (backup, restore, update)
- Handles record validation, conflict resolution, and server communication
- Supports cluster-based configuration and multiple zones

**Configuration System (`pkg/config/config.go`)**
- YAML-based configuration with cluster support
- Handles global Git settings, Vault credentials, and DNS cluster configurations
- Auto-loads from `pulse.yaml`, `config.yaml`, or `./configs/config.yaml`

**Git Integration (`pkg/git/client.go`)**
- GitLab API integration for repository management
- Handles repository creation, cloning, committing, and pushing
- Supports namespace/group hierarchies

**Vault Integration (`pkg/vault/client.go`)**
- HashiCorp Vault client for secret management
- Retrieves DNS API tokens and other sensitive configuration
- Supports both KV v1 and KV v2 secret engines

### Command Structure

Commands are organized using Cobra CLI framework:
- `cmd/root.go` - Main application entry point
- `cmd/dnscmd/` - DNS-specific commands (backup, restore, update, validate)
- `cmd/flags/` - Common flag definitions

### Key Patterns

**Environment Variables**
- `VAULT_TOOLS_ADDR` - Vault server address
- `VAULT_TOOLS_TOKEN` - Vault authentication token  
- `GITLAB_TOKEN` - GitLab API token for Git operations
- `DNS_CLUSTER`, `DNS_ZONES` - Override command flags

**Configuration Hierarchy**
1. Command line flags (highest priority)
2. Environment variables
3. Configuration files (`pulse.yaml` > `config.yaml` > `./configs/config.yaml`)
4. Default values

**Error Handling**
- Custom error types in `pkg/errors/`
- Comprehensive validation for DNS records, zones, and configuration
- Graceful handling of partial failures in batch operations

## Key Files

- `main.go` - Application entry point
- `Makefile` - Build automation and development tasks
- `configs/pulse.yaml` - Example configuration file
- `pkg/dns/manager.go:358` - Core DNS management logic
- `cmd/dnscmd/` - DNS command implementations
- `internal/utils/` - Utility functions (time, path handling)

## Code Guidelines

From `.cursorrules`: Write maintainable, well-documented code as if maintained by someone who knows where you live.

## Testing

Tests are located alongside source files with `_test.go` suffix. Run `make test` to execute all tests or `make test-coverage` for detailed coverage analysis.