# Build stage
FROM registry.eit.zone/external/golang:1.23.4 AS builder

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the binary
ARG VERSION=dev
ARG GIT_COMMIT=unknown
ARG BUILD_DATE=unknown
RUN CGO_ENABLED=0 GOOS=linux go build \
    -ldflags "-X git.homecredit.net/country/vn/platform/pulse/internal/version.Version=${VERSION} \
              -X git.homecredit.net/country/vn/platform/pulse/internal/version.GitCommit=${GIT_COMMIT} \
              -X git.homecredit.net/country/vn/platform/pulse/internal/version.BuildDate=${BUILD_DATE}" \
    -o pulse .

# Final stage
FROM registry.eit.zone/external/alpine:3.21.3

# Add ca-certificates with better error handling
RUN apk update && apk add --no-cache ca-certificates

# Create app user
RUN adduser -D -s /bin/sh appuser

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/pulse /usr/local/bin/pulse

# Make binary executable
RUN chmod +x /usr/local/bin/pulse

# Copy config template
COPY --from=builder /app/configs/pulse.yaml ./configs/

# Change ownership
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser